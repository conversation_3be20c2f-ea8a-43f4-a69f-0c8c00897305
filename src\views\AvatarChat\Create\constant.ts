import ErrorPhoto1 from '@/assets/image/example/error-1.png';
import ErrorPhoto2 from '@/assets/image/example/error-2.png';
import ErrorPhoto3 from '@/assets/image/example/error-3.png';
import ErrorPhoto4 from '@/assets/image/example/error-4.png';

export const DigitalHumanTips = [
  '1、请尽量保持正对摄像头拍摄',
  '2、保持完整头部及双肩出现在镜头内',
  '3、人脸端正，且不可有遮挡脸部等动作',
  '4、保持背景干净整洁，不要出现其他人像',
  '5、请确保上传的图片清晰，否则可能影响定制效果',
];
export const UploadAvatarTips = ['1、数字人将以此形象换脸生成', '2、形象定制后不可用于商业用途'];

export const UploadVideoTips = {
  requirement: [
    '仅1人出镜，脸部清晰可见（宽度占画面1/4以上，但不要超出屏幕）。',
    '脸部无遮挡，眼睛看镜头，保持正面，不要晃动。',
  ],
  content: [
    { key: '前10秒', value: '闭嘴，表情自然，无大动作。' },
    { key: '后10-50秒', value: '说话状态，可带小幅度手势（手别出画面）。' },
  ],
  shoot: [
    { key: '分辨率', value: '720p或1080p（最低360p，最高3840p）。' },
    { key: '帧率', value: '25fps。' },
    { key: '背景', value: '绿色或白色最佳（衣服颜色别和背景相近）。' },
    { key: '固定拍摄', value: '避免镜头晃动，光线和角度保持一致，人物位置不变。' },
    { key: '时长', value: '20秒~1分钟。' },
  ],
};

export const AvatarErrorPhotos = [
  { key: '多张人脸', url: ErrorPhoto1 },
  { key: '人脸太大', url: ErrorPhoto2 },
  { key: '脸部遮挡', url: ErrorPhoto3 },
  { key: '检测不到人脸', url: ErrorPhoto4 },
];

export const TrainModes = [
  {
    disabled: false,
    value: 'fast',
    title: '快速训练',
    tips: '效果一般，训练时间约5分钟',
  },
  {
    disabled: true,
    value: 'precision',
    title: '精准训练',
    tips: '效果较佳，训练时间约5小时',
  },
];

export const theme = {
  components: {
    Radio: {
      // 自定义单选按钮的原点高亮色和边框属性
      dotColorChecked: '#40a9ff', // 高亮时的圆点颜色
      dotBorderColorChecked: '#40a9ff', // 高亮时的边框颜色
      dotSize: 6, // 圆点大小
      radioSize: 12, // 单选框大小
    },
    Spin: {
      contentHeight: 298,
    },
  },
};

export interface RefProps {
  openModal: () => void;
}

export interface DigitalHumanItemProps {
  url: string;
  gender: string;
}
export interface StepProps {
  step: number;
  title: string;
  tooltips?: string[];
}
