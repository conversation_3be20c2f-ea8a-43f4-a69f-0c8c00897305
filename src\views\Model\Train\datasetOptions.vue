<script setup lang="ts">
  import type { IDatasetOptionItems } from '@/interface/dateset';
  import type { ColumnType } from 'ant-design-vue/es/table';
  import { convertIsoTimeToLocalTime } from '@/utils/common';
  import { datasetTypes, dataSourceOptions, formatBytes } from '@/views/Dataset/Components/index';
  import { ref } from 'vue';
  interface IProps {
    visible: boolean;
    dataSource: IDatasetOptionItems[];
  }
  const props = defineProps<IProps>();

  const emit = defineEmits(['update:visible', 'ok', 'cancel', 'after-close']);

  // @ts-expect-error
  const currentRecord = ref<IDatasetOptionItems>({});
  const loading = ref(false);
  const selectedRowKeys = ref<string[]>([]);
  const columns: ColumnType[] = [
    { title: '数据集名称', dataIndex: 'name' },
    { title: '简介', dataIndex: 'summary', width: 200 },
    { title: '数据来源', dataIndex: 'source', width: 120 },
    { title: '数据量', dataIndex: 'data_quantity' },
    { title: '数据大小', dataIndex: 'file_size' },
    { title: '发布时间', dataIndex: 'published_at' },
  ];
  // 处理确定按钮
  const handleOk = async () => {
    emit('update:visible', false);
    const { id, name, url } = currentRecord.value;
    emit('ok', { id, name, url });
  };
  const onSelectChange = (ids: string[]) => {
    selectedRowKeys.value = ids;
    // @ts-expect-error
    currentRecord.value = props.dataSource.find((item) => item.id === ids[0]);
  };
  // 处理取消操作
  const handleCancel = () => {
    emit('update:visible', false);
    emit('cancel');
  };

  defineExpose({
    currentRecord: currentRecord.value,
  });
</script>

<template>
  <a-modal :open="visible" width="70%" centered @cancel="handleCancel" @ok="handleOk">
    <a-table
      :data-source="dataSource"
      :columns="columns"
      :pagination="false"
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'radio' }"
      row-key="id"
      :loading="loading"
      :scroll="{ y: 500 }"
    >
      <template #bodyCell="{ column, record, text }">
        <div v-if="['published_at'].includes(column.dataIndex)">
          {{ convertIsoTimeToLocalTime(text) }}
        </div>
        <div v-else-if="column.dataIndex === 'name'">
          <div>{{ record.name }}</div>
          <div class="text-#797979">{{ record.id }}</div>
        </div>
        <div v-else-if="column.dataIndex === 'type'">
          {{ datasetTypes.find((item) => item.value === text)?.label }}
        </div>
        <div v-else-if="column.dataIndex === 'file_size'">
          {{ formatBytes(text) }}
        </div>
        <div v-else-if="column.dataIndex === 'source'">
          {{ dataSourceOptions.find((item) => item.value === text)?.label }}
        </div>
        <div v-else v-ellipse-tooltip.right>{{ text || '--' }}</div>
      </template>
    </a-table>
  </a-modal>
</template>

<style scoped lang="less"></style>
