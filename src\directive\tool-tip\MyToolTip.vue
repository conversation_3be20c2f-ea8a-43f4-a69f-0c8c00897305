<template>
  <!--提示-->
  <transition name="fade-in-linear">
    <div v-show="tooltipShow" :style="tooltipStyle as any" class="wq-tooltip">
      <span class="wq-tooltip-text" v-text="text"></span>
      <div :class="[placements]" :style="arrowStyle as any" class="wq-tooltip-arrow"></div>
    </div>
  </transition>
</template>
<script lang="ts">
  import { computed, ref } from 'vue';

  export default {
    setup() {
      // 显示弹框
      const tooltipShow = ref(false);

      // 提示内容
      const text = ref();

      // 方向
      const placements = ref('left');

      // 显示
      function showTip() {
        tooltipShow.value = true;
      }

      //设置提示内容
      function setContent(content: any) {
        text.value = content;
      }

      //隐藏
      function hiddenTip() {
        tooltipShow.value = false;
      }

      // 位置
      const tooltipPosition = ref({
        x: 0,
        y: 0,
        width: 0,
        left: 0,
        top: 0,
        right: 0,
        bottom: 0,
      });
      const arrowPosition = ref({
        left: 0,
        top: 0,
        right: 0,
        bottom: 0,
      });
      const tooltipStyle = computed(() => {
        return {
          transform: `translate3d(${tooltipPosition.value.x}px,${tooltipPosition.value.y}px,0)`,
          width: tooltipPosition.value.width ? `${tooltipPosition.value.width}px` : null,
          right: tooltipPosition.value.right ? `${tooltipPosition.value.right}px` : null,
          left: tooltipPosition.value.left ? `${tooltipPosition.value.left}px` : null,
          top: tooltipPosition.value.top ? `${tooltipPosition.value.top}px` : null,
        };
      });
      const arrowStyle = computed(() => {
        return {
          left: arrowPosition.value.left ? `${arrowPosition.value.left}px` : null,
          top: arrowPosition.value.top ? `${arrowPosition.value.top}px` : null,
          right: arrowPosition.value.right ? `${arrowPosition.value.right}px` : null,
          bottom: arrowPosition.value.bottom ? `${arrowPosition.value.bottom}px` : null,
        };
      });
      return {
        tooltipShow,
        showTip,
        hiddenTip,
        setContent,
        tooltipPosition,
        arrowPosition,
        tooltipStyle,
        arrowStyle,
        text,
        placements,
      };
    },
  };
</script>

<style lang="less" scoped>
  // tooltip
  .wq-tooltip {
    position: fixed;
    top: 0;
    z-index: 1000;
    display: block;
    min-width: 30px;
    max-width: fit-content;
    width: 500px;
    padding: 10px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.2;
    color: #fff;
    word-wrap: break-word;
    pointer-events: none;
    background: #303133;
    border-radius: 8px;
  }

  // 小箭头
  .wq-tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px;
  }

  // 如果在左侧
  .wq-tooltip-arrow.left {
    top: 50%;
    right: -15px;
    border-color: transparent transparent transparent #303133;
    transform: translate3d(0, -50%, 0);
  }

  .wq-tooltip-arrow.left-right {
    top: 50%;
    left: -15px;
    border-color: transparent #303133 transparent transparent;
    transform: translate3d(0, -50%, 0);
  }

  // 如果在下侧
  .wq-tooltip-arrow.bottom {
    top: -15px;
    left: 50%;
    border-color: transparent transparent #303133;
    transform: translate3d(-50%, 0, 0);
  }

  // 如果在右侧
  .wq-tooltip-arrow.right {
    top: 50%;
    left: -15px;
    border-color: transparent #303133 transparent transparent;
    transform: translate3d(0, -50%, 0);
  }

  .wq-tooltip-arrow.right-left {
    top: 50%;
    right: -15px;
    border-color: transparent transparent transparent #303133;
    transform: translate3d(0, -50%, 0);
  }

  // 如果在上侧
  .wq-tooltip-arrow.top {
    bottom: -15px;
    left: 50%;
    border-color: #303133 transparent transparent;
    transform: translate3d(-50%, 0, 0);
  }

  /* 动画 */
  .tooltip-enter-from,
  .tooltip-leave-to {
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .tooltip-leave-from,
  .tooltip-enter-to {
    transition: opacity 0.1s ease;
  }
</style>
