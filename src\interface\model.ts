import type { Dayjs } from 'dayjs';
import type { IPage, IPagination } from '.';


export interface IModeListProps extends IPagination {
  name: string;
}
export interface IGetTaskListProps extends IPagination {
  task_name?: string;
  model_name?: string;
  status?: number;
}

export interface ITrainFormState {
  model_id: string;
  tf_parameter: ITFParmeter
  task_name: string;
  max_run_time: number;
  gpu_number_list?: number;
  dataset_parameter: Record<string, unknown>
  description: string;
  docker_image_id: string;
  output_name: string;
  resource: IResource[]
  train_parameter: Train_parameter

  // stage: string;
  // finetuning_type: string;
  // max_run_time_unit: number;
  // dataset_type: number;
  // dataset: string[];
  // model_name: string;
  // resource_type: number;
  // train_parameter: Train_parameter;
  // run_command: string;
}
export interface ICreateTaskProps {
  model_id: string;
  tf_parameter: ITFParmeter
  task_name: string;
  max_run_time: number;
  gpu_number_list?: number[];
  dataset_parameter: IDatasetParameter
  description: string;
  docker_image_id: string;
  output_name: string;
  resource: IResource[]
  train_parameter: Record<string, string>
}

export interface ITFParmeter {
  type: string;
  stage: string;
  finetuning_type: string;
}

export interface IDatasetParameter {
  type?: string;
  name?: string;
  numeric_features?: string[];
  numeric_labels?: string[]
}

export interface IResource {
  key: string;
  GPU: string;
  CPU: string;
  memory: string
}

export interface Train_parameter {
  unsloth_parameters?: General_parameters;
  general_parameters?: General_parameters;
  other_parameters: Other_parameters;
  partial_parameters: Partial_parameters;
  LoRA_parameters: LoRA_parameters;
  RLHF_parameters: RLHF_parameters;
  GaLore_parameters: GaLore_parameters;
  BAdam_parameters: BAdam_parameters;
}

export interface General_parameters {
  learning_rate: string;
  num_train_epochs: number;
  max_grad_norm: number;
  max_samples: number;
  computed_type: string;
  cutoff_len: number;
  per_device_train_batch_size: number;
  gradient_accumulation_steps: number;
  val_size: number;
  lr_scheduler_type: string;
}

export interface Other_parameters {
  logging_steps: number;
  save_steps: number;
  warmup_steps: number;
  neftune_noise_alpha: number;
  /** extra_args: '{"optim": "adamw_torch"}', */
  packing: boolean;
  neat_packing: boolean;
  train_on_prompt: boolean;
  mask_history: boolean;
  resize_vocab: boolean;
  use_llama_pro: boolean;
}

export interface Partial_parameters {
  freeze_trainable_layers: number;
  freeze_trainable_modules: string;
  galore_rank: string;
}

export interface LoRA_parameters {
  lora_rank: number;
  lora_alpha: number;
  lora_dropout: number;
  /** loraplus_lr_ratio: 0, */
  create_new_adapter: boolean;
  use_rslora: boolean;
  use_dora: boolean;
  /**
   * use_pissa: false,
   * additional_target: '',
   */
  lora_target: string;
}

export interface RLHF_parameters {
  pref_beta: number;
  pref_ftx: number;
  pref_loss: string;
  ppo_score_norm: boolean;
  ppo_whiten_rewards: boolean;
}

export interface GaLore_parameters {
  use_galore: boolean;
  galore_rank: number;
  galore_update_interval: number;
  galore_scale: number;
  galore_target: string;
}

export interface BAdam_parameters {
  use_badam: boolean;
  badam_mode: string;
  /** adam_switch_mode: 'ascending', */
  badam_switch_interval: number;
  badam_update_ratio: number;
}

export interface DataItem {
  key?: string;
  GPU: string;
  CPU: string;
  memory: string;
  disabled?: boolean;
}

export interface IModelState {
  modalLoading: boolean;
  visible: boolean;
  type: 'add' | 'edit';
  formState: IModelFormState;
  tagList: ITag[];
  modelCodes: Record<string, string>[];
}
export interface IModelFormState {
  id: string;
  category: string;
  source_path: string;
  source_name: string;
  source_from: string;
  template: string;
  exp_temp: string;
  published_at: Dayjs | string;
  seq: number;
  ops: string[];
  tags: Record<string, string>[];
  output_name?: string;
  output_model_id?: string;
  name: string;
  size: string;
  status?: string;
  inference_engine: Record<string, object | IVllm | ISGLang>;
  engine: string[];
  tf: Record<string, object>
  description: string;
  detail: string;
  tool_template: string
  docker_image_ids: string[];
  docker_images: { id: string, name: string }[];
  dataset_parameter?: IDatasetParameter
  distillation_role?: string;
}

export interface IVllm {
  gpu_memory_utilization: number;
  max_model_len: number
}
export interface ISGLang {
  tool_func_tmp: string;
  mem_fraction_static: number
}


export interface IModelItemResponse extends IModelFormState {
  id: string;
  version: number;
}
export interface ITag {
  name: string;
  id: string;
  seq: number;
  children: ITag[];
}

export interface ITrainMethodsItem {
  name: string;
  value: string;
  desc: string;
  type: ITrainTypesItem[]
}

export interface ITrainTypesItem {
  label: string;
  value: string;
  desc: string;
}

export interface ITrainFrameworksItem {
  name: string;
  value: string;
  desc: string;
}

export interface IOutputModelReq extends IPage {
  name?: string;
  biz_type?: string;
  model_category?: string;
}

export interface IOutputModelItem {
  name: string;
  model_path: string;
  category: string;
  biz_id: string;
  biz_type: string;
  id: string;
  creator_id: string;
  updater_id: string;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface IParameters {
  label: string;
  value: string | number;
  type: string;
  required: boolean;
  min?: number;
  max?: number;
  step?: number;
  precision?: number;
  default?: string | number;
  desc?: string;
  option?: { label: string; value: string }[]
}

export interface IFetchSklearnTaskRequest extends IPage {
  task_name?: string;
  model_name?: string;
  status?: string;
}