<script setup lang="ts">
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table';
  import { ref, reactive, onMounted, watch } from 'vue';
  import { useRoute } from 'vue-router';
  import { convertIsoTimeToLocalTime } from '@/utils/common';
  import type { IPage } from '@/interface';
  import { TABLE_PAGINATION } from '@/json/common';
  import { getIntegrationHistory } from '@/api/integration';
  import type { IIntegrationHistoryItem, ICandidates } from '@/interface/integration';
  interface IProps {
    activeKey: string;
  }
  const props = defineProps<IProps>();
  const route = useRoute();
  const dataSource = ref<IIntegrationHistoryItem[]>([]);
  const tableHeight = ref(0);
  const spinning = ref(false);
  const visible = ref(false);
  const currentRecord = reactive<ICandidates[]>([]);
  const pagination = reactive({ ...TABLE_PAGINATION });
  const pageParame: IPage = reactive({ page: 1, limit: 10 });
  const columns: ColumnType[] = [
    { title: '任务ID', dataIndex: 'id' },
    { title: '问题', dataIndex: 'question' },
    { title: '状态', dataIndex: 'status' },
    { title: route.params.type === 'p2l' ? '较优模型服务' : '初始回答', dataIndex: 'candidates' },
    { title: '最终回答', dataIndex: 'final_result'},
    { title: '创建时间', dataIndex: 'created_at' },
  ];
  const candidateColumns: ColumnType[] = [
    { title: '模型服务', dataIndex: 'model' },
    { title: '回答', dataIndex: 'content' },
  ];
  const statusMap: Record<string, { text: string; color: string }> = {
    failed: {
      text: '失败',
      color: 'red',
    },
    success: {
      text: '成功',
      color: 'green',
    },
  };
  const getTableHeight = () => {
    const tableItem = document.querySelector('.ant-tabs-content');
    tableHeight.value = (tableItem?.clientHeight as number) - 1200;
  };

  const getList = async () => {
    spinning.value = true;
    const data: { items: IIntegrationHistoryItem[]; total: number } = await getIntegrationHistory(
      String(route.params.id),
      pageParame,
    );
    const { items, total } = data;
    dataSource.value = items;
    pagination.total = total;
    spinning.value = false;
  };

  const toggleTable = (_pagination: TablePaginationConfig) => {
    const { current, pageSize } = _pagination;
    Object.assign(pagination, { current, pageSize });
    Object.assign(pageParame, { page: current, limit: pageSize });
    getList();
  };
  const handleCheck = (record: ICandidates[]) => {
    Object.assign(currentRecord, record);
    visible.value = true;
  };

  onMounted(() => {
    getTableHeight();
  });

  watch(
    () => props.activeKey,
    (key) => {
      if (key && key === 'history') {
        getList();
      }
    },
    { deep: true, immediate: true },
  );
</script>

<template>
  <a-table
    :data-source="dataSource"
    :columns="columns"
    :pagination="pagination"
    :scroll="{ y: 520 }"
    :loading="spinning"
    @change="toggleTable"
  >
    <template #bodyCell="{ column, text, record }">
      <div v-if="column.dataIndex === 'candidates'">
        <div v-if="route.params.type === 'p2l'">{{ record.final_result ? record.final_result.svc : '--' }}</div>
        <div v-else>
          <a v-if="text && text.length" @click="handleCheck(text)">查看</a>
          <div v-else>--</div>
        </div>
      </div>
      <div v-else-if="column.dataIndex === 'status'">
        <a-tag :color="statusMap[text].color">{{ statusMap[text].text }}</a-tag>
      </div>
      <div v-else-if="['created_at', 'updated_at'].includes(column.dataIndex)">
        {{ convertIsoTimeToLocalTime(text) }}
      </div>
      <div v-else v-ellipse-tooltip.bottom>{{ text || '--' }}</div>
    </template>
  </a-table>
  <a-modal v-model:open="visible" width="50%" centered title="查看备选回答" :footer="false" @cancel="visible = false">
    <a-table :data-source="currentRecord" :columns="candidateColumns" :pagination="false">
      <template #bodyCell="{ column, text, record }">
        <div v-if="column.dataIndex === 'model'">
          <div>
            {{ text }}
            <span class="text-12px text-#797979 ml-5px">{{ record.svc }}</span>
          </div>
        </div>
        <div v-if="column.dataIndex === 'content'">
          <div v-ellipse-tooltip.bottom>--</div>
        </div>
      </template>
    </a-table>
  </a-modal>
</template>

<style scoped lang="less">
  :deep(.ant-table-cell-ellipsis div) {
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
