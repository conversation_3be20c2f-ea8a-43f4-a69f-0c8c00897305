<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import type { Rule } from 'ant-design-vue/es/form/interface';
  import {
    checkDistillationName,
    checkDistillationOutputName,
    createDistillationTask,
    featchDistillationFillterList,
  } from '@/api/distillation';
  import { LeftOutlined } from '@ant-design/icons-vue';
  import { useRouter, useRoute } from 'vue-router';
  import { dockerDropDown } from '@/api/docker';
  import { distillation_parameters } from '.';
  import { getModelInfoV2 } from '@/api/model';
  import { getRandomCharacters, makeK8sNameValid } from '@/utils/common';
  import type { ICreateDistillationTask } from '@/interface/distillation';
  import { GPUList } from '@/views/Model/Train';
  import { message } from 'ant-design-vue';
  const router = useRouter();
  const route = useRoute();
  const formState = reactive<ICreateDistillationTask>({
    name: '',
    task_type: 'text_generation',
    max_run_time: 600,
    teacher_model_id: '',
    student_model_id: '',
    finetuning_type: '',
    output_name: '',
    gpu_number_list: [],
    dataset_parameter: {
      type: 'system',
      name: 'https://minio-test.shukeyun.com/ai-platform/datasets/admin/7356979681104494592/20250812/gkd_data.jsonl',
    },
    docker_image_id: '',
    train_parameter: {
      lmbda: 0.5,
      beta: 0.5,
      'per-device-train-batch-size': 1,
      'gradient-accumulation-steps': 1,
      'num-train-epochs': 3,
    },
  });

  const dockerList = ref();
  const distillationRef = ref();
  const teacherModels = ref<{ id: string; name: string }[]>([]);
  const studentModels = ref<{ id: string; name: string }[]>([]);
  const trainMethods = ref<{ label: string; value: string; desc: string }[]>([
    {
      label: '全参数',
      value: 'full',
      desc: '全参微调会在模型训练中更新模型的全量参数，一般效果较好，但模型训练时间较长。',
    },
  ]);

  const validatorName = async (_rule: Rule, value: string) => {
    if (value === '' || value.trim().length == 0) {
      return Promise.reject('请输入服务名称');
    }
    const regex = /^[a-z0-9][a-z0-9\-.]*[a-z0-9]$/;
    if (!regex.test(value)) {
      return Promise.reject('格式不正确：仅支持小写字母、数字、-和.，且首尾必须为字母或数字');
    }
    if (value.length > 50) {
      return Promise.reject('任务名称最多输入 50 个字');
    }
    try {
      await checkDistillationName(value.trim());
    } catch (e) {
      if (e === 'AlreadyExists') {
        return Promise.reject('该名称已存在，请重新命名');
      }
      return Promise.reject(e);
    }
    return Promise.resolve();
  };

  const validatorModelName = async (_rule: Rule, value: string) => {
    if (value == '' || value.trim().length == 0) {
      return Promise.reject('请输入模型名称');
    }
    const regex = /^[a-z0-9][a-z0-9\-.]*[a-z0-9]$/;
    if (!regex.test(value)) {
      return Promise.reject('格式不正确：仅支持小写字母、数字、-和.，且首尾必须为字母或数字');
    }
    if (value.length > 50) {
      return Promise.reject('模型名称最多输入 50 个字');
    }
    try {
      await checkDistillationOutputName(value.trim());
    } catch (e) {
      if (e === 'AlreadyExists') {
        return Promise.reject('该名称已存在，请重新命名');
      }
      return Promise.reject(e);
    }
    return Promise.resolve();
  };

  const handleBack = () => {
    router.back();
  };

  const fetchDockerList = async () => {
    const data = await dockerDropDown('distillation');
    dockerList.value = data;
    formState.docker_image_id = data[0].id;
  };

  const confirmAdd = async () => {
    await distillationRef.value.validateFields();

    // @ts-expect-error
    const gpuNumberList = formState.gpu_number_list as number;
    const params: ICreateDistillationTask = JSON.parse(JSON.stringify(formState));
    params.gpu_number_list = [gpuNumberList];
    params.dataset_parameter = null;
    params.name = makeK8sNameValid(formState.name);
    params.output_name = makeK8sNameValid(formState.output_name);
    await createDistillationTask(params);
    message.success('创建成功');
    router.push('/distillation');
  };
  const getDistillationFillterList = async () => {
    const teacher = await featchDistillationFillterList('teacher');
    teacherModels.value = teacher.teacher;
    const student = await featchDistillationFillterList('student');
    studentModels.value = student.student;
  };
  const getModelDetail = async () => {
    const data = await getModelInfoV2(String(route.query.modelId));
    const { distillation_role, id, name } = data;
    if (distillation_role === 'teacher') {
      formState.teacher_model_id = id;
    } else {
      formState.student_model_id = id;
    }
    const customName = `${makeK8sNameValid(`${name}_${getRandomCharacters()}`)}`;
    formState.name = customName;
    formState.output_name = customName;
  };

  onMounted(() => {
    fetchDockerList();
    getDistillationFillterList();
    if (route.query.modelId) {
      getModelDetail();
    }
  });
</script>

<template>
  <div class="wrapper">
    <div class="header">
      <LeftOutlined @click="handleBack" />
      <di class="m-l-10px leading-20px">
        <span class="text-20px">创建蒸馏任务</span>
      </di>
    </div>
    <div class="container overflow-scroll">
      <a-row>
        <a-col :span="15">
          <a-form ref="distillationRef" :model="formState" name="basic" autocomplete="off" layout="vertical">
            <div class="textbefo">训练方式</div>
            <a-form-item label="教师模型" name="teacher_model_id" :rules="[{ required: true }]">
              <div class="model-box">
                <div
                  v-for="item in teacherModels"
                  :key="item.id"
                  class="model-item"
                  :class="{ actived: formState.teacher_model_id === item.id }"
                  @click="formState.teacher_model_id = item.id"
                >
                  {{ item.name }}
                </div>
              </div>
            </a-form-item>
            <a-form-item label="学生模型" name="student_model_id" :rules="[{ required: true }]">
              <div class="model-box">
                <div
                  v-for="item in studentModels"
                  :key="item.id"
                  class="model-item"
                  :class="{ actived: formState.student_model_id === item.id }"
                  @click="formState.student_model_id = item.id"
                >
                  {{ item.name }}
                </div>
              </div>
            </a-form-item>
            <a-form-item label="训练方法" name="finetuning_type" :rules="[{ required: true }]">
              <div class="model-box">
                <div
                  v-for="item in trainMethods"
                  :key="item.value"
                  class="model-item"
                  :class="{ actived: formState.finetuning_type === item.value }"
                  @click="formState.finetuning_type = item.value"
                >
                  {{ item.label }}
                </div>
              </div>
              <span class="text-12px text-#797979">{{
                trainMethods.find((item) => item.value === formState.finetuning_type)?.desc
              }}</span>
            </a-form-item>

            <div class="textbefo">训练设置</div>
            <a-form-item label="任务名称" name="name" :rules="[{ required: true, validator: validatorName }]">
              <a-input v-model:value="formState.name" placeholder="请输入任务名称" show-count :maxlength="50" />
            </a-form-item>
            <a-form-item label="任务类型" name="task_type" :rules="[{ required: true }]">
              <a-select v-model:value="formState.task_type" style="width: 100%">
                <a-select-option value="text_generation">文本生成</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="最大运行时长" name="max_run_time">
              <a-input-number
                v-model:value="formState.max_run_time"
                :precision="0"
                :min="0"
                placeholder="请输入最大运行时长"
                :style="{ width: '100%' }"
              >
                <template #addonAfter> 秒 </template>
              </a-input-number>
            </a-form-item>

            <div class="textbefo">输出配置</div>
            <a-form-item
              label="模型名称"
              name="output_name"
              :rules="[{ required: true, validator: validatorModelName }]"
            >
              <a-input v-model:value="formState.output_name" placeholder="请输入任务名称" show-count :maxlength="50" />
            </a-form-item>

            <div class="textbefo">数据集配置</div>
            <a-row :style="{ marginBottom: '10px' }">
              <a-col :span="6">
                <a-select v-model:value="formState.dataset_parameter!.type" style="width: 100%">
                  <a-select-option value="system">公开数据集</a-select-option>
                  <a-select-option value="private">我的数据集</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="18">
                <div class="dataset">
                  <a-form-item
                    label=""
                    :name="['dataset_parameter', 'name']"
                    :rules="[{ required: true, message: '请选择数据集' }]"
                    :style="{ marginBottom: '0px' }"
                  >
                    <a-select v-model:value="formState.dataset_parameter!.name" style="width: 100%">
                      <a-select-option
                        value="https://minio-test.shukeyun.com/ai-platform/datasets/admin/7356979681104494592/20250812/gkd_data.jsonl"
                        >Chinese-medical-dialogue-data</a-select-option
                      >
                    </a-select>
                  </a-form-item>
                </div>
              </a-col>
            </a-row>

            <div class="textbefo">计算资源配置</div>
            <a-form-item label="GPU 显卡" name="gpu_number_list" :rules="[{ required: true }]">
              <a-select v-model:value="formState.gpu_number_list" placeholder="请选择" style="width: 100%">
                <a-select-option v-for="n in GPUList" :key="n.value" :value="n.value">{{ n.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="镜像选择" name="docker_image_id" :rules="[{ required: true, message: '请选择镜像' }]">
              <a-select v-model:value="formState.docker_image_id" style="width: 100%">
                <a-select-option v-for="n in dockerList" :key="n.id" :value="n.id">{{ n.name }}</a-select-option>
              </a-select>
            </a-form-item>

            <div class="textbefo">超参数配置</div>
            <div class="parameter">
              <div v-for="item in distillation_parameters" :key="item.value" class="parameter-items">
                <a-form-item
                  :name="['train_parameter', item.value]"
                  :rules="[
                    {
                      required: item.required,
                      message: ['input', 'inputNumber'].includes(item.type) ? '请输入' : '请选择',
                    },
                  ]"
                >
                  <template #label>
                    <span>{{ item.label }}</span>
                    <Tooltip v-if="item.desc">
                      <template #title>{{ item.desc }}</template>
                      <QuestionCircleFilled class="ml-5px" />
                    </Tooltip>
                  </template>

                  <a-input-number
                    v-if="item.type === 'inputNumber'"
                    v-model:value="formState.train_parameter[`${item.value}`]"
                    :min="item.min"
                    :max="item.max"
                    :step="item.step || 1"
                    :precision="item.precision || 0"
                    :style="{ width: '100%' }"
                    placeholder="请输入"
                  >
                  </a-input-number>
                </a-form-item>
              </div>
            </div>
          </a-form>
        </a-col>
      </a-row>
    </div>
    <a-row>
      <a-col :span="15">
        <div class="bottom">
          <a-button type="primary" @click="confirmAdd">确定</a-button>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<style scoped lang="less">
  .wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .header {
    height: 36px;
    display: flex;
    align-items: center;
  }
  .container {
    flex: 1;
    // height: 100%;
  }
  .bottom {
    height: 40px;
    display: flex;
    justify-content: end;
    align-items: center;
  }

  .model-box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .model-item {
      border: 1px solid #797979;
      border-radius: 8px;
      padding: 10px 16px;
      cursor: pointer;
      margin: 10px;
      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }
    }
    .actived {
      border-color: #1890ff;
      color: #1890ff;
    }
  }
  .parameter {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: space-between;
    padding: 10px 0;

    .parameter-items {
      flex: 1 1 49%;
      max-width: 49%;
    }
  }
  :deep(.ant-form-item) {
    &:nth-of-type(-n + 3) {
      margin: 0;
    }
  }
</style>
