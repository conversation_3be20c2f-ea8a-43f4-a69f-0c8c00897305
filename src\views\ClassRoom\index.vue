<script setup lang="ts">
  import { ref, onMounted, watch, h, onBeforeUnmount } from 'vue';
  import {
    PlusOutlined,
    SearchOutlined,
    EllipsisOutlined,
    PlayCircleOutlined,
    EditOutlined,
  } from '@ant-design/icons-vue';
  import { getCourse, delProject, synthesisVideo, copyEdit, getAnswer, addAnswer } from '@/api/classRoom';
  import { useRoute, useRouter } from 'vue-router';
  import { getImageCover, getLocalItem } from '@/utils/common';
  import dayjs from 'dayjs';
  import { message } from 'ant-design-vue';
  import empty from '@/assets/image/base/pictures/empty.png';
  import warning from '@/assets/image/base/pictures/warning.png';
  import { LoadingOutlined } from '@ant-design/icons-vue';

  const route = useRoute();
  const router = useRouter();
  const searchName = ref<string>('');
  const activeKey = ref<string>('1');
  const courseData = ref<any>([]);
  const open = ref<boolean>(false);
  const deleteMode = ref<'course' | 'draft'>('course');
  const loading = ref<boolean>(false);
  const choonseCours = ref<any>({});
  const pollInterval = ref<any>(null);
  const selectLens = ref<any>({});
  const fromList = ref<any>([]);
  const userInfo = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');
  //获取课程
  const getCourseData = async (activeKey: string) => {
    loading.value = true;
    courseData.value = [];
    let sign = '';
    if (activeKey == '1') {
      sign = '';
    } else if (activeKey == '2') {
      sign = 'video';
    } else if (activeKey == '3') {
      sign = 'draft';
    }
    let res: any = await getCourse({ user_id: userInfo?.userId, query: sign, search: searchName.value });
    loading.value = false;
    if (res.list) {
      courseData.value = res.list;
    }
  };
  const indicator = h(LoadingOutlined, {
    style: {
      fontSize: '40px',
      color: '#fff',
    },
    spin: true,
  });
  const opendel = (item: any) => {
    choonseCours.value = item;
    open.value = true;
    deleteMode.value = item.kind == 'draft' ? 'draft' : 'course';
  };
  const del = async () => {
    let res: any = await delProject({ ids: [choonseCours.value.id] });
    if (res && res.length > 0) {
      message.success('删除成功');
    } else {
      message.error('删除失败');
    }
    open.value = false;
    getCourseData(activeKey.value);
  };

  onMounted(() => {
    if (route.name == 'classRoom' && !route.params.id) {
      startPolling();
    }
  });
  //开始定时器
  const startPolling = () => {
    if (pollInterval.value) clearInterval(pollInterval.value);
    getCourseData(activeKey.value);
    pollInterval.value = setInterval(() => {
      getCourseData(activeKey.value);
    }, 20000); //20S请求一次
  };
  //清除定时器
  const clearPolling = () => {
    if (pollInterval.value) {
      clearInterval(pollInterval.value);
      pollInterval.value = null;
    }
  };
  const goCreate = () => {
    sessionStorage.removeItem('projectId');
    router.push({ path: `/ClassRoom/addEdit`, query: { modelId: 1 } });
  };
  watch(route, (val) => {
    if (val.name == 'classRoom' && !val.params.id) {
      startPolling();
    } else {
      clearPolling();
    }
  });
  //页面离开时清除定时器
  onBeforeUnmount(() => {
    clearPolling();
  });

  //获取问答
  const getAnswerData = async (imageId: number) => {
    selectLens.value = { id: imageId };
    let res: any = await getAnswer({ image_ids: imageId });
    if (res.list) {
      fromList.value = res.list;
      return fromList.value;
    }
    fromList.value = [];
    return [];
  };

  //打开复制并编辑
  const openCopyEdit = async (id: number) => {
    try {
      // 1. 获取原课程详情，以获取分镜信息
      const courseRes = await getCourse({ id });
      if (!courseRes?.images?.length) {
        message.error('获取课程信息失败');
        return;
      }

      // 2. 复制课程
      const res = await copyEdit({ id });
      if (!res?.id) {
        message.error('复制失败');
        return;
      }

      // 3. 获取新课程详情，以获取新的分镜ID
      const newCourseRes = await getCourse({ id: res.id });
      if (!newCourseRes?.images?.length) {
        message.error('获取新课程信息失败');
        return;
      }

      // 4. 复制每个分镜的问答数据
      for (let i = 0; i < courseRes.images.length; i++) {
        const oldImageId = courseRes.images[i].id;
        const newImageId = newCourseRes.images[i].id;

        // 获取原分镜的问答数据并更新 fromList
        const qaList = await getAnswerData(oldImageId);

        // 使用 fromList 中的数据创建新的问答
        for (const qa of fromList.value) {
          await addAnswer({
            answer: qa.answer,
            image_info_id: newImageId,
            model_name: qa.model_name,
            question: qa.question,
            speaker: qa.speaker,
            true_answer: qa.true_answer,
          });
        }
      }

      // 5. 跳转到编辑页面
      router.push({ path: `/ClassRoom/addEdit`, query: { modelId: 1, classId: res.id } });
    } catch (error) {
      console.error('复制失败:', error);
      message.error('复制失败');
    }
  };

  //打开编辑
  const openEdit = (id: number) => {
    router.push({ path: `/ClassRoom/addEdit`, query: { modelId: 1, classId: id } });
  };
  //切换tab
  const search = (key?: string | MouseEvent) => {
    if (typeof key === 'string') {
      getCourseData(key);
    } else {
      getCourseData(activeKey.value);
    }
  };

  //跳转到详情页
  const goDetails = (id: number) => {
    router.push(`/ClassRoom/${id}`);
  };

  const reGenerate = async (item: any) => {
    // console.log('重新生成', item);
    // console.log(item.images, 'item?.images', item.id, 'item.id');
    let param = {
      data: item?.images?.map((item: any) => ({
        image_id: item.id,
        speaker: item.speaker,
        text: item.text,
        type: item.type === '2d' ? 0 : 1,
      })),
      proj_id: item?.id,
    };
    // console.log(param, 'param');
    let res: any = await synthesisVideo(param);
    if (res.length > 0) {
      message.success('重新生成成功');
      getCourseData(activeKey.value);
    } else {
      message.error('重新生成失败');
    }
  };
</script>

<template>
  <router-view v-if="route.name !== 'classRoom'"></router-view>
  <div v-else class="page">
    <div class="top">
      <a-button type="primary" @click="goCreate">
        <template #icon>
          <PlusOutlined />
        </template>
        创建课程
      </a-button>
      <a-input
        v-model:value="searchName"
        placeholder="搜索课程或草稿"
        allow-clear
        style="width: 408px"
        @press-enter="() => search()"
      >
        <template #prefix>
          <SearchOutlined @click="() => search()" />
        </template>
      </a-input>
    </div>
    <a-tabs v-model:active-key="activeKey" type="card" @change="search">
      <a-tab-pane key="1" tab="全部"> </a-tab-pane>
      <a-tab-pane key="2" tab="我的课程"> </a-tab-pane>
      <a-tab-pane key="3" tab="我的草稿"> </a-tab-pane>
    </a-tabs>
    <div v-if="!loading && courseData.length > 0" class="content">
      <div v-for="(item, index) in courseData" :key="index" class="item" @click="undefined">
        <div class="itemTop" :style="{ backgroundImage: `url(${getImageCover(item.images)})` }">
          <div v-if="item.kind == 'video'" class="maskLayer">
            <!-- 生成中 -->
            <div v-if="item.video_status == 'processing'">
              <a-spin :indicator="indicator" />
              <div style="font-size: 12px; color: #ffffff; margin-top: 8px">生成中</div>
            </div>
            <!-- 失败 -->
            <div v-else-if="item.video_status == 'failed'" class="failedBox">
              <img :src="warning" alt="warning" />
              <div>课程生成失败，请重新生成</div>
              <div class="btnBox" @click.stop="reGenerate(item)">重新生成</div>
            </div>
          </div>
          <div v-if="item.kind !== 'video'" class="maskLayer1" @click.stop="openEdit(item.id)">
            <EditOutlined class="maskIcon" />
          </div>
          <div
            v-if="item.kind == 'video' && item.video_status == 'successed'"
            class="maskLayer1"
            @click="goDetails(item.id)"
          >
            <PlayCircleOutlined class="maskIcon" />
          </div>
        </div>
        <div class="itemBottom">
          <b style="font-size: 14px">{{ item.name }}</b>
          <div class="time editTime">编辑于：{{ dayjs(item.updated_at).format('YYYY-MM-DD HH:mm:ss') }}</div>
          <div class="time createTime">创建于：{{ dayjs(item.created_at).format('YYYY-MM-DD HH:mm:ss') }}</div>
          <a-dropdown placement="bottomRight">
            <EllipsisOutlined :rotate="90" class="icon" />
            <template #overlay>
              <a-menu>
                <a-menu-item
                  v-if="item.kind === 'video' && item.video_status === 'successed'"
                  @click="openCopyEdit(item.id)"
                  >复制并编辑</a-menu-item
                >
                <a-menu-item v-if="item.kind !== 'video' || item.video_status === 'failed'" @click="openEdit(item.id)"
                  >编辑</a-menu-item
                >
                <a-menu-item @click="opendel(item)"> 删除</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
        <div v-if="item.kind == 'draft'" class="draft">草稿</div>
      </div>
    </div>
    <div v-if="!loading && courseData.length == 0" class="content1">
      <div class="emptyBox">
        <img :src="empty" alt="empty" />
        <div>暂无内容</div>
      </div>
    </div>
    <div v-if="loading" class="content1">
      <a-spin size="large" />
    </div>
    <a-modal v-model:open="open" :title="deleteMode === 'course' ? '删除课程' : '删除草稿'" @ok="del">
      <p>确定要删除"{{ choonseCours.name }}"{{ deleteMode === 'course' ? '课程' : '草稿' }}吗？</p>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
  .page {
    width: 100%;
    padding: 20px 20px 0 20px;
    .top {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 25px;
    }
    .content {
      width: 100%;
      border-radius: 5px 5px 0 0;
      overflow-y: auto;
      height: 600px;
      .item {
        display: inline-block;
        width: 18.5%;
        border-radius: 8px 8px 0 0;
        margin-bottom: 40px;
        margin-right: 1.785%;
        position: relative;
        .itemTop {
          width: 100%;
          height: 166px;
          background-size: 100% 100%;
          border-radius: 8px 8px 0 0;
          border: 1px solid #e6e6e6;
          background-color: #f7f7f7;
          border-bottom: none;
          position: relative;
          .maskLayer {
            position: absolute;
            width: 100%;
            height: 100%;
            background: #000000;
            border-radius: 8px 8px 0px 0px;
            opacity: 0.6;
            display: flex;
            justify-content: center;
            align-items: center;
            .maskIcon {
              font-size: 25px;
              color: #fff;
              cursor: pointer;
            }
            .failedBox {
              width: 100%;
              font-weight: 600;
              font-size: 12px;
              color: #ffffff;
              text-align: center;
              > img {
                width: 28px;
                height: 24px;
                margin-bottom: 8px;
              }
              .btnBox {
                width: 80px;
                height: 30px;
                background: rgba(24, 25, 26, 0.9);
                border-radius: 4px;
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 12px auto 0;
                cursor: pointer;
              }
            }
          }
        }
        .maskLayer1 {
          position: absolute;
          width: 100%;
          height: 100%;
          background: #000000;
          border-radius: 8px 8px 0px 0px;
          opacity: 0.6;
          text-align: center;
          display: none;
          .maskIcon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 25px;
            color: #fff;
            cursor: pointer;
          }
        }

        .itemBottom {
          position: relative;
          width: 100%;
          background-color: #fff;
          padding: 12px 12px 14px 12px;
          border: 1px solid #e6e6e6;
          border-top: none;
          border-radius: 0 0 8px 8px;
          .editTime {
            margin-top: 6px;
          }
          .time {
            color: #969799;
            margin-top: 4px;
            font-size: 12px;
          }
          .icon {
            position: absolute;
            font-size: 20px;
            color: #000000;
            right: 20px;
            bottom: 20px;
            cursor: pointer;
          }
        }
      }
      .item:hover .maskLayer1 {
        display: block;
      }
      .draft {
        position: absolute;
        top: 10px;
        left: 12px;
        width: 44px;
        height: 26px;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 4px;
        backdrop-filter: blur(2px);
        font-size: 12px;
        color: #f5f6f7;
        text-align: center;
        line-height: 26px;
      }
      .item:hover {
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.12);
      }
      .item:nth-child(5n) {
        margin-right: 0;
      }
    }
    .content1 {
      width: 100%;
      overflow-y: auto;
      height: 600px;
      display: flex;
      justify-content: center;
      align-items: center;
      .emptyBox {
        text-align: center;
        font-size: 14px;
        color: #636466;
        > img {
          width: 142px;
          height: 100px;
          margin-bottom: 12px;
        }
      }
    }
  }
</style>
