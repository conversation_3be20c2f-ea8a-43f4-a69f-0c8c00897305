<script setup lang="ts">
  import type { IPage } from '@/interface';
  import { TABLE_PAGINATION } from '@/json/common';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table';
  import { ref, reactive, watch } from 'vue';
  import { convertIsoTimeToLocalTime, debounce } from '@/utils/common';
  import { useRouter, useRoute } from 'vue-router';
  import type { Rule } from 'ant-design-vue/es/form';
  import { deleteKnowledgedb, editKnowledgedb, fetchKnowledgedbList } from '@/api/knowledgebase.ts';
  import { message } from 'ant-design-vue';
  import type { IKnowledgedb } from '@/interface/knowledge';
  import { PlusOutlined } from '@ant-design/icons-vue';

  const router = useRouter();
  const route = useRoute();
  const editFormRef = ref();
  const loading = ref(false);
  const DEFAULT_NAME = '';
  const name = ref(DEFAULT_NAME);
  const currentRecord = reactive<{ id: string; name: string; description: string }>({
    id: '',
    name: '',
    description: '',
  });
  const visible = reactive({
    delete: false,
    edit: false,
  });
  const columns: ColumnType[] = [
    { title: '知识库名称', dataIndex: 'name' },
    { title: '知识库描述', dataIndex: 'description', width: 180 },
    { title: '文件数量', dataIndex: 'files', align: 'right', width: 100 },
    { title: '向量模型', dataIndex: 'embedding_model', width: 180 },
    { title: '知识库 ID', dataIndex: 'id' },
    // { title: '创建人', dataIndex: 'source_path' },
    { title: '创建时间', dataIndex: 'created_at' },
    { title: '最近编辑时间', dataIndex: 'updated_at' },
    { title: '操作', dataIndex: 'operation', fixed: 'right', width: 140 },
  ];
  const pagination = reactive({ ...TABLE_PAGINATION });
  const pageParame: IPage = reactive({ page: 1, limit: 10 });
  const dataSource = ref<IKnowledgedb[]>([]);
  const handleAdd = () => {
    router.push(`/knowledge/add`);
  };
  const onSearch = () => {
    fetchData();
  };
  const toggleTable = (_pagination: TablePaginationConfig) => {
    const { current, pageSize } = _pagination;
    Object.assign(pagination, { current, pageSize });
    Object.assign(pageParame, { page: current, limit: pageSize });
    fetchData();
  };
  const fetchData = async () => {
    loading.value = true;
    const data: { items: IKnowledgedb[]; total: number } = await fetchKnowledgedbList({
      name: name.value,
      ...pageParame,
    });
    const { items, total } = data;
    dataSource.value = items;
    Object.assign(pagination, { current: pageParame.page, total: total });
    loading.value = false;
  };
  const debouncedSearch = debounce(fetchData);
  const clickInfo = (record: IKnowledgedb) => {
    router.push(`/knowledge/file/${record.id}?name=${record.name}`);
  };
  const handleEdit = (record: IKnowledgedb) => {
    const { name, description, id } = record;
    Object.assign(currentRecord, { name, description, id });
    visible.edit = true;
  };
  const handleDelete = (record: IKnowledgedb) => {
    const { id, name } = record;
    Object.assign(currentRecord, { id, name });
    visible.delete = true;
  };
  const confirmDelete = async () => {
    await deleteKnowledgedb(currentRecord.id);
    visible.delete = false;
    message.success(`${currentRecord.name}已删除`);
    fetchData();
  };
  const confirmEdit = async () => {
    await editFormRef.value.validateFields();
    const { name, description } = currentRecord;
    await editKnowledgedb({
      db_id: currentRecord.id,
      name,
      description,
    });
    message.success(`${currentRecord.name}已编辑`);
    fetchData();
    visible.edit = false;
  };
  const handleCancel = async () => {
    // await editFormRef.value.resetFields();
    await editFormRef.value.clearValidate();
  };
  const validatorName = (_rule: Rule, value: string) => {
    if (value == '' || value.trim().length == 0) {
      return Promise.reject('请输入知识库名称');
    }
    if (value.length > 50) {
      return Promise.reject('模型名称最多输入 50 个字');
    }
    return Promise.resolve();
  };

  watch(
    () => route.path,
    (path) => {
      if (path === '/knowledge') {
        fetchData();
      }
    },
    { deep: true, immediate: true },
  );
</script>

<template>
  <template v-if="route.params.id || ['/knowledge/add', '/knowledge/export'].includes(route.path)">
    <router-view></router-view>
  </template>
  <template v-else>
    <div class="flex justify-between w-100% m-b-12px">
      <a-button type="primary" @click="handleAdd"><plus-outlined />创建知识库</a-button>
      <a-input-search
        v-model:value="name"
        placeholder="搜索知识库名称"
        allow-clear
        style="width: 400px"
        @change="debouncedSearch"
        @blur="onSearch"
      ></a-input-search>
    </div>
    <a-table
      :data-source="dataSource"
      :columns="columns"
      :pagination="pagination"
      :loading="loading"
      :scroll="{ y: 600 }"
      @change="toggleTable"
    >
      <!-- <template #emptyText>
        <a-empty :description="name === DEFAULT_NAME ? '你还没有创建知识库哦，快去创建吧' : '暂无相关知识库'"></a-empty>
      </template> -->
      <template #bodyCell="{ column, record, text }">
        <div v-if="column.dataIndex === 'operation'" class="operation-box">
          <a @click="clickInfo(record)">查看</a>
          <a @click="handleEdit(record)">编辑</a>
          <a class="del-btn" @click="handleDelete(record)">删除</a>
        </div>

        <div v-else-if="column.dataIndex === 'files'">
          {{ text.length }}
        </div>
        <div v-else-if="['created_at', 'updated_at'].includes(column.dataIndex)">
          {{ convertIsoTimeToLocalTime(text) }}
        </div>
        <div v-else v-ellipse-tooltip.right class="max-w-200px">{{ text || '--' }}</div>
      </template>
    </a-table>
    <a-modal
      v-model:open="visible.delete"
      centered
      :title="`确定删除知识库“${currentRecord.name}”？`"
      @ok="confirmDelete"
    >
      <p>删除后不可恢复</p>
    </a-modal>
    <a-modal
      v-model:open="visible.edit"
      width="50%"
      centered
      title="编辑知识库"
      @ok="confirmEdit"
      @cancel="handleCancel"
    >
      <a-form
        ref="editFormRef"
        autocomplete="off"
        :model="currentRecord"
        v-bind="{
          labelCol: { span: 3 },
          wrapperCol: { span: 22 },
        }"
      >
        <a-form-item label="知识库名称" name="name" :rules="[{ required: true, validator: validatorName }]">
          <a-input v-model:value="currentRecord.name" style="width: 100%" show-count :maxlength="50" />
        </a-form-item>
        <a-form-item label="知识库描述" name="description">
          <a-textarea
            v-model:value="currentRecord.description"
            allow-clear
            show-count
            :maxlength="500"
            :auto-size="{ minRows: 2, maxRows: 6 }"
            placeholder="请输入"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </template>
</template>
<style scoped lang="less"></style>
