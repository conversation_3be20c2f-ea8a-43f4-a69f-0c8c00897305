<script setup lang="ts">
  import { ref } from 'vue';

  const props = defineProps<{
    visible?: boolean;
    handleCloseModal?: () => void;
  }>();

  const emit = defineEmits(['close', 'agree']);

  const isOpenModal = ref(false);

  // 链接协议弹窗相关
  const agreementModalVisible = ref(false);
  const agreementModalTitle = ref('');
  const agreementContent = ref('');

  // 协议内容数据
  const agreementData = {
    service: {
      title: '服务条款',
      content: `一、服务条款

  1. 服务说明
  本服务条款是您与我们之间关于使用本平台服务的法律协议。通过使用本服务，您同意遵守本条款的所有规定。

  2. 服务内容
  我们提供 AI 智能对话、嵌入代码生成等相关服务。服务内容可能会根据业务发展需要进行调整。

  3. 用户义务
  - 您应当合法使用本服务，不得用于任何违法违规目的
  - 您应当保护好自己的账户信息，对账户下的所有活动负责
  - 您不得恶意攻击、破坏本服务的正常运行

  4. 服务限制
  - 我们有权根据实际情况对服务进行维护、升级或暂停
  - 对于免费服务，我们不承诺服务的持续性和稳定性

  5. 知识产权
  本服务中的所有内容，包括但不限于文字、图片、音频、视频、软件等，均受知识产权法保护。

  6. 免责声明
  在法律允许的最大范围内，我们对因使用本服务而产生的任何直接或间接损失不承担责任。

  7. 条款变更
  我们有权随时修改本服务条款，修改后的条款将在平台上公布。继续使用服务即表示您接受修改后的条款。`,
    },
    user: {
      title: '用户协议',
      content: `一、用户协议

  1. 协议范围
  本用户协议适用于所有使用本平台服务的用户。注册或使用本服务即表示您同意本协议的全部内容。

  2. 账户注册
  - 您应当提供真实、准确的注册信息
  - 您应当及时更新注册信息，确保信息的有效性
  - 一个手机号码或邮箱只能注册一个账户

  3. 账户安全
  - 您应当妥善保管账户密码，不得向他人透露
  - 如发现账户被盗用，应立即通知我们
  - 您对账户下的所有操作承担责任

  4. 使用规范
  - 不得发布违法、有害、威胁、辱骂、骚扰、侵权的内容
  - 不得传播垃圾信息、广告信息或恶意软件
  - 不得干扰或破坏服务的正常运行

  5. 内容权利
  - 您对自己发布的内容拥有知识产权
  - 您授权我们在提供服务过程中使用您的内容
  - 我们有权删除违规内容

  6. 服务变更
  我们有权随时修改、暂停或终止服务，并会提前通知用户。

  7. 违约处理
  如您违反本协议，我们有权采取警告、限制功能、暂停或终止服务等措施。

  8. 争议解决
  因本协议产生的争议，双方应友好协商解决；协商不成的，提交有管辖权的人民法院解决。`,
    },
    privacy: {
      title: '隐私条款',
      content: `一、隐私条款

  1. 隐私保护承诺
  我们高度重视用户隐私保护，本隐私条款说明我们如何收集、使用、存储和保护您的个人信息。

  2. 信息收集
  我们可能收集以下信息：
  - 注册信息：用户名、邮箱、手机号码等
  - 使用信息：登录记录、操作日志、设备信息等
  - 内容信息：您在使用服务过程中产生的对话内容、上传的文件等

  3. 信息使用
  我们收集信息的目的：
  - 提供和改进服务
  - 用户身份验证和账户安全
  - 客户服务和技术支持
  - 产品分析和优化

  4. 信息共享
  我们不会向第三方出售、出租或交易您的个人信息，除非：
  - 获得您的明确同意
  - 法律法规要求
  - 保护我们的合法权益

  5. 信息存储
  - 我们采用行业标准的安全措施保护您的信息
  - 信息存储在安全的服务器中，采用加密技术
  - 我们会定期审查和更新安全措施

  6. 信息访问和控制
  您有权：
  - 访问和更新您的个人信息
  - 删除您的账户和相关信息
  - 选择接收或拒绝营销信息

  7. Cookie 使用
  我们使用 Cookie 来改善用户体验，您可以通过浏览器设置控制 Cookie 的使用。

  8. 未成年人保护
  我们不会故意收集未满 18 岁用户的个人信息。如发现，我们会立即删除相关信息。

  9. 隐私条款更新
  我们可能会更新本隐私条款，更新后会在平台上公布。继续使用服务即表示您接受更新后的条款。

  10. 联系我们
  如您对隐私保护有任何问题，请通过平台客服联系我们。`,
    },
  };

  const openModal = () => {
    isOpenModal.value = true;
  };

  defineExpose({
    openModal,
  });

  const handleOk = () => {
    isOpenModal.value = false;
    emit('agree');
    emit('close');
  };

  const handleCancel = () => {
    isOpenModal.value = false;
    emit('close');
  };

  // 处理协议链接点击
  const handleAgreementClick = (type: 'service' | 'user' | 'privacy') => {
    const agreement = agreementData[type];
    agreementModalTitle.value = agreement.title;
    agreementContent.value = agreement.content;
    agreementModalVisible.value = true;
  };

  // 关闭协议弹窗
  const handleAgreementClose = () => {
    agreementModalVisible.value = false;
  };
</script>

<template>
  <a-modal
    :open="isOpenModal"
    :footer="null"
    :width="900"
    :mask-closable="false"
    centered
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <template #title>
      <!-- <ExclamationCircleOutlined style="margin-right: 8px" /> -->
      使用者承诺须知
    </template>
    <div class="line_bot"></div>
    <div class="user-rights-notice">
      <div class="notice-content">
        <p class="notice-text">
          本声明将帮助您更好的在【环球数科AI中台】平台（下称'本平台'）使用相关工具上传和管理您的作品。您若上传作品，即视为您已充分知悉并充分接受以下内容：
        </p>

        <p class="notice-text">
          1. 您作为使用者在本平台上传、发布的作品，应具有独立、完整的知识产权，不得侵犯他人知识产权等任何权利。
        </p>

        <p class="notice-text">
          2.
          您在使用本平台及上传、发布作品时，应当自觉遵守国家法律、法规，遵守公共秩序，尊重社会公德、社会主义制度、国家利益、公民合法权益、道德风尚和信息真实性等要求。如有违反，一经本平台发现将根据违规程度采取包括但不限于删除、下架、禁止发布内容、封禁账号等处理方式。如造成恶劣影响或涉嫌违法犯罪的，本平台将有权向有关管理机关或公安机关提交相关内容，并配合进行调查。
        </p>

        <p class="notice-text">
          3.
          若您上传的作品及作品中的素材（包括但不限于创意、文本、肖像、音频、图片、视频等）侵犯了任何第三方权利，本平台均有权在收到相关侵权投诉后对该被投诉的作品或用户账号依据相应规则，采取包括但不限于下架、警告、封禁账号等处理方式。
        </p>

        <p class="notice-text">
          4.
          请勿使用我们的服务克隆或生成任何侵犯版权、违反道德伦理、或违反中华人民共和国法律法规的内容。我们生成的所有内容均带有详细日志，自动/人工复审，以及可溯源的隐形视频/音频水印，若发现您违反了相关规则，我们保留终止您的服务并上报公安机关等机构的权利。
        </p>

        <p class="notice-text">
          5. 更多信息请参阅
          <a class="link" @click="handleAgreementClick('user')">《用户协议》</a>
          <a class="link" @click="handleAgreementClick('privacy')">《隐私条款》</a>。
        </p>
      </div>

      <div class="notice-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk">我已知晓，同意</a-button>
      </div>
    </div>
    <!-- 协议详情弹窗 -->
    <a-modal
      v-model:open="agreementModalVisible"
      :title="agreementModalTitle"
      :width="800"
      :style="{ height: '700px', top: '50px', bottom: '50px' }"
      :footer="null"
      class="agreement-modal"
      @cancel="handleAgreementClose"
    >
      <div class="agreement-content">
        <div class="agreement-text">{{ agreementContent }}</div>
      </div>
    </a-modal>
  </a-modal>
</template>

<style lang="less" scoped>
  .user-rights-notice {
    border-top: 1px solid #f0f1f2;
    .notice-content {
      max-height: 400px;
      overflow-y: auto;
      padding: 16px 0;

      .notice-text {
        font-size: 14px;
        line-height: 1.6;
        color: #333;
        margin-bottom: 12px;
        text-align: justify;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .link-text {
        color: #1890ff;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .notice-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
      padding-top: 16px;
      // border-top: 1px solid #f0f0f0;
    }
  }
  .line_top {
    height: 1px;
    background-color: #f0f1f2;
    margin: 12px 0 20px;
    position: absolute;
    left: 0;
    right: 0;
  }
  .line_bot {
    height: 1px;
    background-color: #f0f1f2;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 92px;
  }

  /* 协议弹窗样式 */
  .agreement-modal :deep(.ant-modal-header) {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
  }

  .agreement-modal :deep(.ant-modal-body) {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
  }

  .agreement-content {
    font-size: 14px;
    line-height: 1.6;
  }

  .agreement-text {
    color: #262626;
    white-space: pre-line;
  }

  :global(.agreement-modal .ant-modal-header) {
  }

  // :global(.agreement-modal .ant-modal-body) {
  //   padding-left: 30px;
  //   padding-right: 30px;
  //   padding-bottom: 20px;
  // } // TODO: 后边研究学习
</style>
