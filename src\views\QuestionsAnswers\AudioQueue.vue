<script lang="ts" setup>
  import { ref, watch, onUnmounted } from 'vue';

  interface Props {
    audioUrls: string[]; // 音频URL数组
  }

  const props = defineProps<Props>();

  // 音频状态
  const currentAudio = ref<HTMLAudioElement | null>(null);
  const currentIndex = ref(-1);
  const queue = ref<string[]>([]);

  // 记录已播放的音频URL
  const playedUrls = ref<Set<string>>(new Set());

  // 停止当前播放
  const stopCurrent = () => {
    console.log('Stopping current audio...'); // 添加日志
    if (currentAudio.value) {
      try {
        currentAudio.value.pause();
        currentAudio.value.currentTime = 0;
        currentAudio.value = null;
      } catch (error) {
        console.error('Error stopping current audio:', error);
      }
    }
  };

  // 停止所有音频播放
  const stopAllAudio = () => {
    console.log('Stopping all audio in queue...'); // 添加日志
    stopCurrent();
    queue.value = [];
    currentIndex.value = -1;
    playedUrls.value.clear();
  };

  // 组件卸载时确保停止播放
  onUnmounted(() => {
    console.log('AudioQueue unmounting, stopping all audio...'); // 添加日志
    stopAllAudio();
  });

  // 暴露方法给父组件
  defineExpose({
    stopAllAudio,
    stopCurrent,
  });

  // 播放下一个音频
  const playNext = () => {
    // 如果当前有音频在播放，先停止
    if (currentAudio.value) {
      currentAudio.value.pause();
      currentAudio.value = null;
    }

    // 移动到下一个索引
    currentIndex.value++;

    // 检查是否还有音频需要播放
    if (currentIndex.value >= queue.value.length) {
      return;
    }

    const currentUrl = queue.value[currentIndex.value];

    // 如果当前音频已经播放过，跳到下一个
    if (playedUrls.value.has(currentUrl)) {
      playNext();
      return;
    }

    // 创建新的音频实例
    const audio = new Audio(currentUrl);
    currentAudio.value = audio;

    // 设置音频事件监听
    audio.addEventListener('ended', () => {
      // 标记当前URL为已播放
      playedUrls.value.add(currentUrl);
      playNext();
    });

    audio.addEventListener('error', () => {
      playNext();
    });

    // 开始播放（处理浏览器自动播放策略）
    audio.play().catch((err) => {
      // 如果因为没有用户手势而被拦截，等待一次用户点击/触摸后再尝试
      if (err && (err.name === 'NotAllowedError' || err.name === 'AbortError')) {
        const onUserGesture = () => {
          document.removeEventListener('click', onUserGesture);
          document.removeEventListener('touchstart', onUserGesture);
          // 再次尝试播放当前音频；失败则继续下一个
          if (currentAudio.value === audio) {
            audio.play().catch(() => {
              playNext();
            });
          } else {
            playNext();
          }
        };
        document.addEventListener('click', onUserGesture, { once: true });
        document.addEventListener('touchstart', onUserGesture, { once: true });
        return;
      }
      // 其他错误直接播放下一个
      playNext();
    });
  };

  // 监听音频列表变化
  watch(
    () => props.audioUrls,
    (newUrls) => {
      console.log('Audio URLs changed:', newUrls); // 添加日志
      queue.value = [...newUrls];

      // 检查是否有未播放的音频
      const hasUnplayedAudio = newUrls.some((url) => !playedUrls.value.has(url));

      // 如果有新的未播放音频，开始播放
      if (hasUnplayedAudio) {
        currentIndex.value = -1;
        playNext();
      }
    },
    { deep: true, immediate: true }, // 添加immediate: true确保初始化时也会执行
  );
</script>

<template>
  <div class="audio-queue"></div>
</template>

<style scoped>
  .audio-queue {
    display: none;
  }
</style>
