<script setup lang="ts">
  import { ref } from 'vue';

  import { PlusOutlined, LoadingOutlined, UploadOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';

  import { getLocalItem } from '@/utils/common';
  import { getModeDigitals, getPreview, strtPretrain, uploadDigital } from '@/api/avatarChat';
  import Step from './Step.vue';
  import { DigitalHumanTips, UploadAvatarTips, theme } from './constant';
  import type { DigitalHumanItemProps } from './constant';
  import UserRightsNotice from './UserRightsNotice.vue';

  // import { Loading } from '@/components';
  import DefaultDigitalPreviewImg from '@/assets/image/base/pictures/digital-preview-icon.png';
  import DigitalHumanSpeakersModal from '../Speakers/index.vue';

  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');

  const props = defineProps<{
    handleCloseModal: () => void;
  }>();

  const isOpenModal = ref(false);
  const digitalHumanLoading = ref(false);
  const loading = ref(false);
  const pageLoading = ref(false);
  const trainMode = ref('fast');
  const digitalList = ref<{ url: string; gender: string }[]>([{ url: '', gender: '' }]);
  const selectedDigitalMan = ref({ index: 0, url: '', gender: '' });
  const uploadAvatarUrl = ref('');
  const preViewUrl = ref('');
  const name = ref('');
  const isNameEntered = ref(true);
  const isAvatarUploaded = ref(true);

  // 添加选中声音的状态
  const selectedSpeaker = ref<{ name: string; description: string } | null>(null);

  const speakersModalRef = ref();

  // 版权校验相关状态
  const isAgreedToTerms = ref(false);
  const userRightsNoticeRef = ref();

  const openModal = () => {
    isOpenModal.value = true;
    trainMode.value = 'fast';
    uploadAvatarUrl.value = '';
    preViewUrl.value = '';
    selectedDigitalMan.value = { index: 0, url: '', gender: '' };
    isNameEntered.value = true;
    isAvatarUploaded.value = true;
    selectedSpeaker.value = null; // 重置选中的声音
    queryDigitalHumanList();
  };

  defineExpose({
    openModal,
  });

  const handlePreview = () => {
    console.log('preview');
    isAvatarUploaded.value = Boolean(uploadAvatarUrl.value);
    if (!uploadAvatarUrl.value) {
      return;
    }

    // 每次点击都要设置 loading
    pageLoading.value = true;
    preViewUrl.value = '';
    getPreview({
      category: trainMode.value,
      source_path: uploadAvatarUrl.value,
      target_path: selectedDigitalMan.value.url,
      speaker: selectedSpeaker.value?.name,
      user_id: userId,
    })
      .then((url: string) => {
        preViewUrl.value = url as string;
      })
      .catch(() => {
        console.log('预览失败');
        // message.error('预览失败');
      })
      .finally(() => {
        pageLoading.value = false;
      });
  };

  const handleTrain = async () => {
    isNameEntered.value = Boolean(name.value);
    if (!name.value) {
      return;
    }

    // 检查是否勾选了协议
    if (!isAgreedToTerms.value) {
      userRightsNoticeRef.value?.openModal();
      return;
    }

    pageLoading.value = true;
    // const isCartoon = selectedDigitalMan.value.index === digitalList.value.length - 1;
    strtPretrain({
      name: name.value,
      category: trainMode.value,
      image_url: preViewUrl.value,
      source_path: uploadAvatarUrl.value,
      is_cartoon: false,
      target_path: selectedDigitalMan.value.url,
      gender: selectedDigitalMan.value.gender,
      user_id: userId,
      speaker: selectedSpeaker.value?.name,
    })
      .then(() => {
        props.handleCloseModal();
        isOpenModal.value = false;
        isAgreedToTerms.value = false;
        message.success('已经开始训练');
      })
      .catch(() => {
        message.error('训练失败');
      })
      .finally(() => {
        pageLoading.value = false;
      });
  };

  // const handleTrainModeChange = (value: string) => {
  //   trainMode.value = value;
  // };

  const handleDigitalModeChange = (index: number, item: DigitalHumanItemProps) => {
    selectedDigitalMan.value = { index, ...item };
    // console.log(selectedDigitalMan.value);
  };

  const handleNameChange = (value = '') => {
    const newName = value;
    console.log(newName);
    isNameEntered.value = Boolean(newName);
    name.value = newName;
  };

  const handleOk = () => {
    isOpenModal.value = false;
  };

  const handleCancel = () => {
    isOpenModal.value = false;
  };

  const queryDigitalHumanList = async () => {
    digitalHumanLoading.value = true;
    getModeDigitals()
      .then((data: any) => {
        digitalList.value = data;
        selectedDigitalMan.value = { index: 0, ...data[0] };
      })
      .finally(() => {
        digitalHumanLoading.value = false;
      });
  };

  const uploadProps = {
    beforeUpload: (file: File) => {
      const is = ['image/png', 'image/jpg', 'image/jpeg'].includes(file.type);
      if (!is) {
        message.error('请上传jpg、jpeg、png格式图片');
      }
      return is;
    },
    customRequest: async (detail: { file: File }) => {
      const file = detail.file;
      const formData = new FormData();
      formData.append('face', file);
      loading.value = true;
      uploadDigital(formData, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      })
        .then((data: any) => {
          uploadAvatarUrl.value = data?.[0];
          isAvatarUploaded.value = data?.[0] ? true : false;
        })
        .catch(() => {
          message.error('上传失败，请上传五官清晰的人脸照片');
        })
        .finally(() => {
          loading.value = false;
        });
    },
    multiple: false,
    fileList: [],
    accept: 'image/png,image/jpg,image/jpeg',
    showUploadList: false,
  };

  // 打开模态框
  const handleOpenSpeakersModal = () => {
    console.log('打开选择声音模态框', speakersModalRef.value);
    speakersModalRef.value?.openSpeakersModal();
  };

  const handleCloseSpeakersModal = () => {
    console.log('关闭选择声音模态框');
  };

  // 处理声音选择
  const handleSpeakerSelected = (speaker: { name: string; description: string }) => {
    selectedSpeaker.value = speaker;
    console.log('选中的声音:', speaker);
  };

  // 处理用户同意协议
  const handleUserAgreeTerms = () => {
    isAgreedToTerms.value = true;
  };

  // 打开协议弹窗
  const handleOpenUserRightsNotice = () => {
    userRightsNoticeRef.value?.openModal();
  };
</script>

<template>
  <a-modal
    :open="isOpenModal"
    title="变装数字人"
    :footer="null"
    :width="1122"
    :mask-closable="false"
    centered
    :style="{ height: '830px' }"
    :body-style="{ overflow: 'hidden' }"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <!-- <a-spin :spinning="pageLoading" size="large" tip="加载中..." wrapper-class-name="custom-spin"> -->
    <div class="create-digital-man-modal">
      <div class="create">
        <div class="line_top"></div>
        <!-- <Step :step="1" title="训练方式" />
        <a-config-provider :theme="theme">
          <a-radio-group v-model:value="trainMode" class="custom-radio-group">
            <div
              v-for="{ value, title, tips, disabled } in TrainModes"
              :key="title"
              :class="{
                'radio-item': true,
                selected: trainMode === value,
                disabled: disabled,
              }"
              @click="!disabled && handleTrainModeChange(value)"
            >
              <a-radio :disabled="disabled" :value="value">
                {{ title }}
              </a-radio>
              <div class="desc">{{ tips }}</div>
            </div>
          </a-radio-group>
        </a-config-provider> -->

        <div class="custom-upload-avatar">
          <Step :step="1" title="上传头像" :tooltips="DigitalHumanTips" :required="true" />
          <!-- <p class="avatar-format-tip">仅支持上传清晰人脸照（jpg、jpeg、png格式）</p> -->

          <a-upload
            v-bind="uploadProps"
            list-type="picture-card"
            :class="{ 'avatar-uploader': true, error: !isAvatarUploaded }"
          >
            <template #default>
              <div v-if="uploadAvatarUrl">
                <div class="upload-preview">
                  <img v-if="uploadAvatarUrl" :src="uploadAvatarUrl" alt="avatar" class="avatar-icon" />
                  <div class="upload-overlay">
                    <UploadOutlined class="re-upload-icon" />

                    重新上传
                  </div>
                </div>
              </div>
              <div v-else>
                <div class="upload-icon-box">
                  <button type="button" class="upload-button">
                    <template v-if="loading">
                      <LoadingOutlined />
                    </template>
                    <template v-else>
                      <PlusOutlined
                        class="upload-plus-icon"
                        style="font-size: 24px; font-weight: 600; color: #969799; margin-bottom: 12px"
                      />
                      <p class="upload-tip">将图片拖放此处或者点击上传</p>
                      <p class="upload-format-tip">支持jpg、jpeg、png格式</p>
                    </template>
                  </button>
                  <!-- <span v-if="!isAvatarUploaded" class="error-msg">请上传头像</span> -->
                </div>
              </div>
            </template>
          </a-upload>
          <span v-if="!isAvatarUploaded" class="error-msg">请上传头像</span>
        </div>

        <div class="format-tip">
          <Step :step="2" title="参考形象" :tooltips="UploadAvatarTips" :required="true" />
          <!-- <p class="avatar-format-tip">(数字人将以此形象换脸生成)</p> -->
        </div>

        <div class="digital-human-list">
          <a-config-provider :theme="theme">
            <div class="scroll-box">
              <div
                v-for="({ url, gender }, index) in digitalList.filter((item) => item.url)"
                :key="url"
                :class="{
                  'grid-item': true,
                  active: selectedDigitalMan.index === index,
                }"
                @click="handleDigitalModeChange(index, { url, gender })"
              >
                <img v-if="url" :src="url" alt="" />
              </div>
            </div>
          </a-config-provider>
        </div>

        <!-- <div class="format-tip">
          <Step :step="3" title="声音" />
          <p class="avatar-format-tip">(非必选，如设置，生成的数字人将有对应声)</p>
        </div> -->

        <!-- <div class="speakers-list custom-speakers-section">
          <a-input
            block
            class="speakers-btn"
            :value="selectedSpeaker?.description || ''"
            placeholder="请选择声音"
            readonly
            @click="handleOpenSpeakersModal"
          >
            <template #suffix>
              <RightOutlined />
            </template>
          </a-input>
        </div> -->
        <div class="line_bot"></div>
        <div class="compose-preview-btn" @click="handlePreview">生成预览</div>
      </div>

      <div class="preview">
        <template v-if="preViewUrl === ''">
          <div class="preview-init" style="position: relative; min-height: 300px">
            <template v-if="pageLoading">
              <div
                style="
                  position: absolute;
                  left: 0;
                  top: 0;
                  width: 100%;
                  height: 100%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  z-index: 10;
                  background: #f7f8fa;
                "
              >
                <a-spin :spinning="true" tip="正在生成预览，请耐心等待" class="loading-spin">
                  <template #indicator>
                    <LoadingOutlined style="font-size: 60px" spin />
                  </template>
                </a-spin>
                <!-- <a-spin :spinning="true" size="large" tip="加载中..." /> -->
              </div>
            </template>
            <template v-else>
              <img :src="DefaultDigitalPreviewImg" alt="" />
              <!-- <div class="preview-title">数字人预览</div> -->
              <div class="preview-tip">请根据左侧提示操作，生成后的数字人形象将在此预览</div>
            </template>
          </div>
        </template>
        <template v-else>
          <div class="preview-train">
            <div class="preview-content">
              <!-- <Step :step="4" title="数字人预览" /> -->
              <div class="mt-20px mb-13px whitespace-nowrap">
                <span class="text-14px text-#17181A font-600">数字人预览</span>
                <span class="text-12px text-#636466"
                  >（此处只做形象预览，确认生成后，可在“我的数字人”中查看动态效果）</span
                >
              </div>
              <img class="train-img" :src="preViewUrl" alt="" />
              <div class="preview-footer">
                <a-input
                  v-model="name"
                  :status="!isNameEntered ? 'error' : ''"
                  :class="{ 'preview-input': true, error: !isNameEntered }"
                  placeholder="请输入数字人名称"
                  :maxlength="10"
                  show-count
                  @change="(e: Event) => handleNameChange((e.target as HTMLInputElement).value)"
                />
                <a-button :disabled="pageLoading" type="primary" class="preview-btn" @click="handleTrain">
                  确认生成
                </a-button>
              </div>
              <!-- 版权校验勾选框 -->
              <div class="terms-agreement">
                <a-checkbox v-model:checked="isAgreedToTerms"> 我已阅读并同意 </a-checkbox>
                <a href="javascript:void(0)" class="terms-link" @click="handleOpenUserRightsNotice">
                  《使用者承诺须知》
                </a>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <digitalHumanSpeakersModal
      ref="speakersModalRef"
      :handle-close-modal="handleCloseSpeakersModal"
      @speaker-selected="handleSpeakerSelected"
    />
    <UserRightsNotice ref="userRightsNoticeRef" @agree="handleUserAgreeTerms" />
    <!-- </a-spin> -->
  </a-modal>
</template>

<!-- <style lang="less" src="./custom.less"></style>
<style lang="less" src="./custom-antd.less"></style> -->
<style scoped lang="less">
  .create-digital-man-modal {
    // :global(.ant-modal .ant-modal-content) {
    //   // padding: 0 30px 20px;
    //   padding: 20px 0 0;
    // }
    // :global(.ant-modal .ant-modal-header) {
    //   padding-left: 30px;
    // }
    // .create {
    //   padding-left: 30px;
    // }
    // .compose-preview-btn {
    //   margin-bottom: 20px;
    // }
    .line_top {
      top: 50px;
    }
  }
</style>
