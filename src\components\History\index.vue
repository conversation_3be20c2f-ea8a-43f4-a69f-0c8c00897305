<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { CloseOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import type { IHistoryItems, Messages } from '.';
  import { convertIsoTimeToLocalTime } from '@/utils/common';
  interface IProps {
    visible: boolean;
    data: IHistoryItems[];
    loading: boolean;
    history_id: string;
  }
  const props = withDefaults(defineProps<IProps>(), {
    visible: false,
  });
  const emits = defineEmits<{
    (event: 'update:visible', value: boolean): void;
    (event: 'fetchData'): void;
    (event: 'select', data: { history_id: string; messages: Messages[] }): void;
    (event: 'delete', id: string): void;
  }>();
  const historyRef = ref();
  const historyIconRef = ref();
  const handleClick = (e: Event) => {
    if (historyIconRef.value.contains(e.target)) {
      emits('update:visible', true);
      if (!props.visible) {
        emits('fetchData');
      }
    } else if (!historyRef.value.contains(e.target)) {
      // 检查是否点击了弹窗元素
      const target = e.target as HTMLElement;
      const isModal =
        target.closest('.ant-modal') ||
        target.closest('.modal') ||
        target.closest('[role="dialog"]') ||
        target.closest('.popup') ||
        target.closest('.dialog');

      // 如果点击了弹窗，不执行关闭操作
      if (isModal) {
        return;
      }

      // 如果点击了目标元素以外的元素，关闭弹窗
      emits('update:visible', false);
    }
  };

  /**
   * 判断时间是否是今天
   * @param isoTime ISO时间字符串
   * @returns 是否是今天
   */
  function isToday(isoTime: string): boolean {
    if (!isoTime) return false;

    const inputDate = new Date(isoTime);
    const today = new Date();

    return (
      inputDate.getFullYear() === today.getFullYear() &&
      inputDate.getMonth() === today.getMonth() &&
      inputDate.getDate() === today.getDate()
    );
  }

  /**
   * 判断时间是否是昨天
   * @param isoTime ISO时间字符串
   * @returns 是否是昨天
   */
  function isYesterday(isoTime: string): boolean {
    if (!isoTime) return false;

    const inputDate = new Date(isoTime);
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    return (
      inputDate.getFullYear() === yesterday.getFullYear() &&
      inputDate.getMonth() === yesterday.getMonth() &&
      inputDate.getDate() === yesterday.getDate()
    );
  }
  const formatDate = (date: string) => {
    let time = '';
    if (isToday(date)) {
      time = '今天';
    } else if (isYesterday(date)) {
      time = '昨天';
    } else {
      time = convertIsoTimeToLocalTime(date);
    }
    return time;
  };
  const handleClickHistory = (record: IHistoryItems) => {
    const { history_id, messages } = record;
    emits('select', { history_id, messages });
    emits('update:visible', false);
  };
  const handleDelete = (id: string) => {
    emits('delete', id);
  };
  const handleClose = () => {
    emits('update:visible', false);
  };
  onMounted(() => {
    window.addEventListener('click', handleClick);
  });

  onUnmounted(() => {
    window.removeEventListener('click', handleClick);
  });
</script>

<template>
  <div class="history">
    <a-tooltip>
      <template #title> 历史会话 </template>
      <div ref="historyIconRef">
        <svg class="history-icon" aria-hidden="true">
          <use xlink:href="#icon-lishijilu"></use>
        </svg>
      </div>
    </a-tooltip>
  </div>
  <div v-show="visible" ref="historyRef" class="hitory-wrapper w-100% h-100%">
    <div class="header">
      <div class="header-content">
        <div>历史会话</div>
        <span class="text-14px text-#797979 ml-10px">仅保留最近30条对话记录，每条记录至多进行30轮对话</span>
      </div>
      <div class="close" @click="handleClose"><CloseOutlined /></div>
    </div>
    <a-spin :spinning="loading">
      <div class="content overflow-scroll">
        <template v-if="data.length">
          <div
            v-for="item in data"
            :key="item.history_id"
            class="history-item"
            :class="{ actived: history_id === item.history_id }"
            @click="handleClickHistory(item)"
          >
            <div class="history-item-left">
              <div class="title">{{ item.messages[0] ? item.messages[0].content : '--' }}</div>
              <div class="date">
                {{ formatDate(item.updated_at) }}
              </div>
            </div>
            <div class="history-item-right">
              <DeleteOutlined @click.stop="handleDelete(item.history_id)" />
            </div>
          </div>
        </template>
        <div v-else class="empty">
          <a-empty description="暂无历史会话" />
        </div>
      </div>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
  .history {
    position: absolute;
    right: 0;
    top: 0;
    .history-icon {
      width: 24px;
      height: 24px;
      cursor: pointer;
      &:hover {
        opacity: 0.5;
      }
    }
  }
  .hitory-wrapper {
    position: absolute;
    right: 0;
    top: 30px;
    border: 1px solid #ccc;
    border-radius: 10px;
    background-color: #fff;
    width: 40%;
    height: 500px;
    box-shadow: 0px 2px 6px 0px rgba(69, 70, 73, 0.2);
  }
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ccc;
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      > div {
        font-size: 16px;
        font-weight: bold;
      }
    }
    .close {
      cursor: pointer;
    }
  }
  .content {
    height: calc(100% - 60px);
    padding: 10px;
    border-radius: 10px;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
    .history-item {
      height: 60px;
      width: 100%;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;

      &:hover {
        background-color: #f6f8fb;
        .title {
          color: #0046d7;
        }
        .history-item-right {
          display: block;
        }
      }
      .history-item-left {
        width: calc(100% - 30px);
        display: flex;
        justify-content: space-between;
        .title {
          overflow: hidden; /* 超出部分隐藏 */
          white-space: nowrap; /* 强制不换行 */
          text-overflow: ellipsis; /* 超出部分显示省略号 */
          width: calc(100% - 40px);
        }
      }
      .history-item-right {
        display: none;
        width: 20px;
        margin-left: 10px;
      }
    }
    .actived {
      background-color: #f6f8fb;
      .title {
        color: #0046d7;
      }
      .history-item-right {
        display: block;
      }
    }
    .empty {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
</style>
