<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { copyText } from '@/utils/common';
import { message, Modal } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import { PlusOutlined, MinusCircleOutlined, FileTextOutlined, EditOutlined } from '@ant-design/icons-vue';
import type { ColumnType } from 'ant-design-vue/es/table';
import EmbedDetailModal from './EmbedDetailModal.vue';
import type { IAgentItem } from '@/interface/agent';
import dayjs from 'dayjs';
import { publishChannel } from '@/api/agent';
import { useRoute } from 'vue-router';

// 定义 props
interface Props {
  agentId: string;
  agentDetail?: IAgentItem;
}
const props = defineProps<Props>();
const emit = defineEmits<{
  (event: 'agent-detail-update'): void;
}>();

// 发布渠道数据接口
interface PublishChannel {
  key: string;
  channel: string;
  channelIcon: string;
  description: string;
  status: 'published' | 'unpublished';
  publishTime?: string;
  actions: string[];
}

// 已配置网站数据接口
interface ConfiguredWebsite {
  website_name: string;
  domain: string;
  agentUrl?: string;
}

// 表格数据
const dataSource = ref<PublishChannel[]>([
  {
    key: '1',
    channel: '网页版',
    channelIcon: 'globe',
    description: '可通过 PC 端浏览器访问的网页版本对话',
    status: 'published',
    publishTime: '2025.07.30 11:36:24',
    actions: ['visit', 'copy']
  },
  {
    key: '2',
    channel: '网站嵌入',
    channelIcon: 'code',
    description: '通过 iframe/JS 嵌入到其他网站或应用的嵌入式体验版本',
    status: 'unpublished',
    actions: ['website']
  }
]);

// 已配置网站数据（如需使用可在此处接入真实数据）

// 表格列配置
const columns: ColumnType[] = [
  {
    title: '发布渠道',
    dataIndex: 'channel',
    key: 'channel',
    width: 200,
  },
  {
    title: '发布结果',
    dataIndex: 'status',
    key: 'status',
    width: 200,
  },
  {
    title: '操作',
    key: 'actions',
    width: 300,
  },
];

// 弹窗状态
const isModalVisible = ref(false);
const isEmbedDetailVisible = ref(false);
const selectedWebsite = ref<ConfiguredWebsite | null>(null);
const route = useRoute();

// 编辑模式状态
const isEditMode = ref(false);
const editingWebsiteIndex = ref(-1);

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
interface FormData {
  websiteName: string;
  domains: string[];
}

const formData = reactive<FormData>({
  websiteName: '',
  domains: []
});

// 表单验证规则
const rules = {
  websiteName: [
    { required: true, message: '请输入网站名称', trigger: 'blur' },
    { max: 30, message: '网站名称不能超过 30 个字符', trigger: 'blur' }
  ]
};

// 操作处理函数
const handleVisit = () => {
  const href = publishConfig.value.agentUrl
  window.open(href);
};

const handleCopyLink = () => {
    const href = publishConfig.value.agentUrl
    copyText(href);
};

const publishConfig = computed(() => {
  const { publish_channel, origins } = props.agentDetail || {};
  const host = location.href.split('#')[0];
  console.log('props.agentDeta====')
  console.log(props.agentDetail)
  return {
    isWebPublish: publish_channel?.includes('website') || false,
    publish_channel: publish_channel || [],
    origins: Array.isArray(origins) ? origins : [],
    agentUrl: `${host}#/intelligent/preview/${props.agentDetail?.master_id}`,
    publishTime: dayjs(props.agentDetail?.updated_at).format('YYYY.MM.DD HH:mm:ss')
  };
});

const handleGoToWebsite = () => {
    isEditMode.value = false;
    editingWebsiteIndex.value = -1;
    isModalVisible.value = true;
};

// 处理编辑网站
const handleEditWebsite = (website: any, index: number) => {
  isEditMode.value = true;
  editingWebsiteIndex.value = index;

  // 填充表单数据 - 处理不同的数据结构
  if (typeof website === 'object' && website !== null) {
    formData.websiteName = website.website_name || '';
    formData.domains = website.domain ? website.domain.split(',') : [];
  } else {
    // 如果是字符串或其他类型，设置默认值
    formData.websiteName = String(website) || '';
    formData.domains = [];
  }

  isModalVisible.value = true;
};

// 处理删除网站
const handleDeleteWebsite = (website: any, index: number) => {

  Modal.confirm({
    title: '确定删除网站？',
    content: `删除后该网站将无法访问应用服务`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        const currentOrigins = publishConfig.value.origins || [];

        // 从数组中移除指定索引的网站数据
        const updatedOrigins = currentOrigins.filter((_, i) => i !== index);

        // 调用接口更新后端数据
        await publishChannel(String(route.params.id), {
          publish_channel: ['website', 'embedded'],
          origins: updatedOrigins
        });

        message.success('网站删除成功');
        emit('agent-detail-update');
      } catch (error) {
        console.error('删除网站失败：', error);
        message.error('删除网站失败，请重试');
      }
    }
  });
};

// 弹窗相关函数
const handleModalOk = async () => {
  try {
    // 验证表单
    await formRef.value?.validate();


    // 提交前校验域名格式
    if (!validateDomainsBeforeSubmit()) {
      return;
    }

    const processedDomains = (formData.domains || [])
      .map((d) => (d || '').trim())
      .filter((d) => d);
    const newWebsiteData = {
      website_name: formData.websiteName,
      domain: processedDomains.join(',')
    };

    let updatedOrigins: any[];
    const currentOrigins = (publishConfig.value.origins || []) as any[];

    if (isEditMode.value) {
      // 编辑模式：更新指定索引的网站数据
      updatedOrigins = [...currentOrigins];
      updatedOrigins[editingWebsiteIndex.value] = newWebsiteData;
    } else {
      // 新增模式：添加新的网站数据
      updatedOrigins = [...currentOrigins, newWebsiteData];
    }

    await publishChannel(String(route.params.id), {
      publish_channel: ['website', 'embedded'],
      origins: updatedOrigins
    });

    // message.success(isEditMode.value ? '网站编辑成功' : '网站添加成功');
    // 关闭新增/编辑弹窗
    isModalVisible.value = false;

    // 关闭后立即打开对应的嵌入详情弹窗
    selectedWebsite.value = {
      website_name: newWebsiteData.website_name,
      domain: newWebsiteData.domain,
      agentUrl: publishConfig.value.agentUrl
    };
    isEmbedDetailVisible.value = true;

    // 重置表单并通知上层刷新
    resetForm();
    emit('agent-detail-update');
  } catch (error) {
    console.log('表单验证失败：', error);
  }
};

const handleModalCancel = () => {
  isModalVisible.value = false;
  resetForm();
};

const resetForm = () => {
  formData.websiteName = '';
  formData.domains = [];
  formRef.value?.resetFields();
  isEditMode.value = false;
  editingWebsiteIndex.value = -1;
};

// 域名输入框相关函数
const addDomain = () => {
  if (formData.domains.length < 10) {
    formData.domains.push('');
  }
};

const removeDomain = (index: number) => {
  formData.domains.splice(index, 1);
};

	// 域名格式校验（支持 http/https、子域名、localhost、IPv4，可选端口，允许结尾/）
	const isValidDomainUrl = (val: string) => {
	  if (!val) return false;
	  const regex = /^https?:\/\/((localhost)|(([A-Za-z0-9-]+\.)+[A-Za-z]{2,})|((25[0-5]|2[0-4]\d|1?\d?\d)(\.(25[0-5]|2[0-4]\d|1?\d?\d)){3}))(?:\:\d{1,5})?\/?$/;
	  return regex.test(val);
	};

// AntD 表单规则：域名校验（失焦触发），空值不报错
const domainRules = [
  {
    validator: (_rule: any, value: string) => {
      const val = (value || '').trim();
      if (!val) return Promise.resolve();
      return isValidDomainUrl(val)
        ? Promise.resolve()
        : Promise.reject('域名格式不正确，请输入以 http/https 开头的完整域名');
    },
    trigger: 'blur',
  },
];

	// 提交前仅做网站名称重复校验（字段级校验由 AntD 规则完成）
	const validateDomainsBeforeSubmit = () => {
	  const inputName = (formData.websiteName || '').trim();
	  if (inputName) {
	    const origins = (publishConfig.value?.origins || []) as any[];
	    const exists = origins.some((item: any, idx: number) => {
	      if (isEditMode.value && idx === editingWebsiteIndex.value) return false; // 编辑时忽略自身
	      const name = (typeof item === 'object' && item)
	        ? (item as any).website_name
	        : String(item);
	      return (name || '').trim() === inputName;
	    });
	    if (exists) {
	      message.error(`网站名称 '${inputName}' 已存在，请使用不同的名称`);
	      return false;
	    }
	  }
	  return true;
	};



// 处理详情按钮点击
const handleShowDetail = (website: any) => {
  console.log('website')
  console.log(website)
  selectedWebsite.value = {
    website_name: website.website_name || website,
    domain: website.domain || '',
    agentUrl: publishConfig.value.agentUrl
  };
  isEmbedDetailVisible.value = true;
};

// 处理详情弹窗关闭
const handleEmbedDetailClose = () => {
  isEmbedDetailVisible.value = false;
  selectedWebsite.value = null;
};

// 处理从详情弹窗中编辑网站
const handleEditWebsiteFromDetail = () => {
  if (selectedWebsite.value) {
    // 关闭详情弹窗
    isEmbedDetailVisible.value = false;

    // 找到当前网站在 origins 数组中的索引
    const websiteIndex = publishConfig.value.origins.findIndex((website: any) => {
      return website.website_name === selectedWebsite.value?.website_name;
    });

    if (websiteIndex !== -1) {
      // 打开编辑弹窗
      handleEditWebsite(selectedWebsite.value, websiteIndex);
    }

    // 清空选中的网站
    selectedWebsite.value = null;
  }
};
</script>

<template>
  <div class="publish-tab-container">
    <div class="publish-header">
      <h3 class="publish-title">发布渠道及结果如下</h3>
    </div>

    <div class="publish-table-wrapper">
      <a-table
        :data-source="dataSource"
        :columns="columns"
        :pagination="false"
        :bordered="true"
        size="middle"
      >
        <!-- 发布渠道列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'channel'">
            <div class="channel-cell">
              <div class="channel-icon">
                <img
                  v-if="record.channelIcon === 'globe'"
                  src="@/assets/image/base/pictures/web-icon.png"
                  alt="网页版"
                  class="channel-icon-img"
                />
                <img
                  v-else-if="record.channelIcon === 'code'"
                  src="@/assets/image/base/pictures/web-embedding.png"
                  alt="网站嵌入"
                  class="channel-icon-img"
                />
              </div>
              <div class="channel-info">
                <div class="channel-name">{{ record.channel }}</div>
                <div class="channel-desc">{{ record.description }}</div>
              </div>
            </div>
          </template>

          <!-- 发布结果列 -->
          <template v-else-if="column.dataIndex === 'status'">
            <div class="status-cell">
              <div v-if="record.status === 'published'" class="status-published">
                <div class="status-row">
                  <a-tag v-if="publishConfig.isWebPublish" color="success">已发布</a-tag>
                  <a-tag v-else color="warning">未发布</a-tag>
                </div>
                <div class="publish-time">{{ publishConfig.publishTime }}</div>
              </div>
              <div v-else class="status-unpublished">
                <a-tag v-if="publishConfig.origins.length === 0" color="warning">未发布</a-tag>
                <a-tag v-else color="success">已发布</a-tag>
              </div>
            </div>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <div class="actions-cell">
              <template v-if="record.status === 'published'">
                <a-button
                type="link"
                  size="small"
                  class="action-btn"
                  @click="handleVisit"
                >
                  立即访问
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  class="action-btn"
                  @click="handleCopyLink"
                >
                  复制智能体链接
                </a-button>
              </template>
              <template v-else>
                <!-- 已配置网站列表 -->
                <div class="configured-websites">
                  <div v-for="(website, index) in publishConfig.origins" :key="index" class="website-item">
                    <div class="website-info">
                      <div class="website-name">{{ (website as any)?.website_name || website }}</div>
                      <div class="website-domain-count">已配置{{ (website as any)?.domain ? (website as any).domain.split(',').length : 0 }}个域名</div>
                    </div>
                    <div class="website-actions">
                      <a-tooltip placement="top" title="嵌入详情">
                        <a-button type="text" size="small" class="action-icon-btn" @click="handleShowDetail(website)">
                          <template #icon>
                            <FileTextOutlined />
                          </template>
                        </a-button>
                      </a-tooltip>
                      <a-tooltip placement="top" title="修改网站配置">
                        <a-button type="text" size="small" class="action-icon-btn" @click="handleEditWebsite(website, index)">
                          <template #icon>
                            <EditOutlined />
                          </template>
                        </a-button>
                      </a-tooltip>
                      <a-tooltip placement="top" title="删除网站">
                        <a-button type="text" size="small" class="action-icon-btn danger" @click="handleDeleteWebsite(website, index)">
                          <template #icon>
                            <MinusCircleOutlined />
                          </template>
                        </a-button>
                      </a-tooltip>
                    </div>
                  </div>
                </div>

                <a-button
                  type="primary"
                  size="small"
                  @click="handleGoToWebsite"
                >
                  新增网站
                </a-button>
              </template>
            </div>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 新增/编辑网站弹窗 -->
    <a-modal
      v-model:open="isModalVisible"
      :title="isEditMode ? '修改网站配置' : '新增网站'"
      :width="600"
      :ok-text="isEditMode ? '完成并查看代码' : '完成并生成代码'"
      cancel-text="取消"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <div class="modal-content">
        <div v-if="isEditMode" class="edit-tip">
          修改网站配置不影响已生成的嵌入代码
        </div>
        <a-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          layout="vertical"
        >
          <!-- 网站名称 -->
          <a-form-item
            label="网站名称"
            name="websiteName"
            class="form-item"
          >
            <a-input
              v-model:value="formData.websiteName"
              placeholder="请输入网站名称"
              :maxlength="30"
              :show-count="true"
              class="form-input"
            />
          </a-form-item>

          <!-- 网站域名 -->
          <a-form-item
            label="网站域名"
            class="form-item"
          >
            <div class="domain-description">
              配置需要调用智能体的域名。为提升资源安全性，请按实际使用情况填写正确的域名。如不配置域名，则不限制请求来源
            </div>

            <!-- 域名输入框列表 -->
            <div class="domain-inputs">
              <div
                v-for="(_, index) in formData.domains"
                :key="index"
                class="domain-input-row"
              >
                <a-form-item :name="['domains', index]" :rules="domainRules" class="domain-input-col">
                  <a-input
                    v-model:value="formData.domains[index]"
                    placeholder="请填写需要接入的网站域名，如：https://hqshuke.com/"
                    class="domain-input"
                  />
                </a-form-item>
                <MinusCircleOutlined
                  class="remove-domain-btn"
                  @click="removeDomain(index)"
                />
              </div>
            </div>

            <!-- 新增域名按钮 -->
            <div class="add-domain-btn-wrapper">
              <a-button
                v-if="formData.domains.length < 10"
                type="dashed"
                class="add-domain-btn"
                @click="addDomain"
              >
                <PlusOutlined />
                新增域名
              </a-button>
            </div>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 嵌入详情弹窗 -->
    <EmbedDetailModal
      v-if="selectedWebsite"
      :visible="isEmbedDetailVisible"
      :website-info="selectedWebsite"
      @update:visible="isEmbedDetailVisible = $event"
      @close="handleEmbedDetailClose"
      @edit-website="handleEditWebsiteFromDetail"
    />
  </div>
</template>

<style scoped>
.publish-tab-container {
  padding: 20px;
  height: 100%;
  background: #fff;
}

.publish-header {
  margin-bottom: 20px;
}

.publish-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin: 0;
}

.publish-table-wrapper {
  background: #fff;
}

/* 发布渠道列样式 */
.channel-cell {
  display: flex;
  align-items: flex-start;
  gap: 6px;
}

.channel-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  /* width: 24px; */
  /* height: 24px; */
  margin-top: 2px;
}

.channel-icon-img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.channel-info {
  flex: 1;
}

.channel-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.channel-desc {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
}

/* 发布结果列样式 */
.status-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-published {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-row {
  display: flex;
  align-items: center;
}

.status-unpublished {
  display: flex;
  align-items: center;
}

.publish-time {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

/* 操作列样式 */
.actions-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
}

.action-btn {
  padding: 0;
  height: auto;
  line-height: 1.4;
}

.action-btn + .action-btn {
  margin-top: 8px;
}

/* 已配置网站列表样式 */
.configured-websites {
  width: 100%;
  /* margin-bottom: 16px; */
}

.website-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.website-item:last-child {
  margin-bottom: 0;
}

.website-info {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.website-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.website-domain-count {
  font-size: 12px;
  color: #8c8c8c;
}

.website-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-icon-btn {
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: color 0.3s;
}

.action-icon-btn:hover {
  color: #1890ff;
}

.action-icon-btn.danger:hover {
  color: #ff4d4f;
}

/* 表格样式调整 */
:deep(.ant-table) {
  border-radius: 6px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 500;
  color: #262626;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 16px;
  vertical-align: top;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

/* 弹窗样式 */
.modal-content {
  padding: 20px 0;
}

.edit-tip {
  margin-bottom: 16px;
  padding: 10px 12px;
  font-size: 12px;
  color: #1f1f1f;
  background: #f0f5ff;
  /* border: 1px solid #adc6ff; */
  border-radius: 4px;
}

.form-item {
  margin-bottom: 24px;
}

.form-input {
  width: 100%;
}

/* Ant Design 表单样式调整 */
:deep(.ant-form-item-label > label) {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

:deep(.ant-form-item) {
  margin-bottom: 24px;
}

.domain-description {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.5;
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 4px;
}

.domain-inputs {
  margin-bottom: 16px;
}

.domain-input-row {
  display: flex;
  align-items: start;
  gap: 8px;
  margin-bottom: 12px;
}

.domain-input-col {
  flex: 1;
}

.domain-input {
  flex: 1;
}

.remove-domain-btn {
  margin-top: 5px;
  font-size: 16px;
  color: #000;
  cursor: pointer;
  padding: 4px;
  transition: color 0.3s;
}

.remove-domain-btn:hover {
  color: #ff4d4f;
}

.add-domain-btn-wrapper {
  display: flex;
  justify-content: flex-start;
}

.add-domain-btn {
  height: 40px;
  border: 1px dashed #d9d9d9;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 0 16px;
}

.add-domain-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.domain-error {
  margin-top: 4px;
  font-size: 12px;
  line-height: 1.5;
  color: #ff4d4f;
}
</style>
