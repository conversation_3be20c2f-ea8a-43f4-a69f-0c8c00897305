<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { getLocalItem } from '@/utils/common';
  import { changeBackground } from '@/api/avatarChat';

  interface BackgroundProps {
    isCollapsed: boolean;
    backgroundData: string[];
    contextId: string;
    isInit: boolean;
    selectedBackground?: string;
    type?: 'public' | 'private'; // 区分公共数字人或我的数字人
    selectedRoleSource?: string; // 角色来源：'library' 或 'my'
  }

  // Props
  const props = defineProps<BackgroundProps>();
  const emit = defineEmits(['background-selected']);

  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');

  // 当前选中的背景图片索引
  const selectedIndex = ref<number | null>(0);
  // 上一次选中的背景图片索引
  const previousIndex = ref<number | null>(0);

  // 初始化选中状态
  const initializeSelection = () => {
    if (props.selectedBackground) {
      const index = filteredBackgroundData.value.findIndex((bg) => bg === props.selectedBackground);
      if (index !== -1) {
        selectedIndex.value = index;
        previousIndex.value = index;
      }
    }
  };

  // 过滤掉包含 "default.jpg" 的图片
  const filteredBackgroundData = computed(() => props.backgroundData?.filter((item) => !item.includes('default.jpg')));

  // 监听 filteredBackgroundData 变化，重新初始化选中状态
  watch(
    filteredBackgroundData,
    () => {
      initializeSelection();
    },
    { immediate: true },
  );

  // 判断文件类型是否为图片
  const isImage = (file: string) => /\.(jpg|jpeg|png|gif|webp)$/i.test(file);

  // 判断文件类型是否为视频
  const isVideo = (file: string) => /\.(mp4|webm|ogg)$/i.test(file);

  // 更换背景数据
  const changeBackgroundData = async (bg_url: string) => {
    try {
      await changeBackground({
        bg_url: bg_url,
        user_id: userId,
        context_id: props.contextId,
        type: props.type || 'public', // 使用传入的type参数，默认为'public'
      });
      // setTimeout(() => {
      //   message.success('背景更换成功！');
      // }, 2000);
    } catch (error) {
      setTimeout(() => {
        message.error('背景更换失败！');
        selectedIndex.value = previousIndex.value; // 恢复到之前的选中项
      }, 2000);
    }
  };

  // 处理背景选择
  const handleSelect = (index: number) => {
    previousIndex.value = selectedIndex.value; // 保存当前选中的索引为上一次选中的背景索引
    selectedIndex.value = index; // 设置选中项

    // 获取选中的背景URL并通知父组件
    const selectedBackgroundUrl = filteredBackgroundData.value[index];
    if (selectedBackgroundUrl) {
      emit('background-selected', selectedBackgroundUrl);
    }
  };
</script>

<template>
  <div class="background-container" :style="{ overflow: props.isCollapsed ? 'hidden' : '324px' }">
    <div class="background-container-images">
      <div
        v-for="(item, index) in filteredBackgroundData"
        :key="index"
        class="imageBox"
        :class="['imageBox', { selected: selectedIndex === index, disabled: props.selectedRoleSource === 'my' }]"
        @click="
          () => {
            handleSelect(index);
            // 当selectedRoleSource为'my'时，不调用changeBackgroundData
            props.isInit && props.selectedRoleSource !== 'my' && changeBackgroundData(item);
          }
        "
      >
        <template v-if="isImage(item)">
          <img class="img" :src="item" :alt="`背景 ${index}`" />
        </template>
        <template v-else-if="isVideo(item)">
          <video class="video" :src="item" autoplay />
        </template>
        <template v-else>
          <div>不支持的文件类型</div>
        </template>
      </div>
    </div>
  </div>
</template>
<style scoped>
  .background-container {
    width: 100%;
    height: auto;
    .background-container-images {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      max-height: 630px; /* 设置固定高度 */
      overflow-y: auto; /* 启用纵向滚动 */
      overflow-x: hidden; /* 禁用横向滚动 */
      background: #ffffff;
      box-shadow: 4px 2px 24px 0px rgba(123, 123, 123, 0.1);

      /* 隐藏滚动条 */
      ::-webkit-scrollbar {
        display: none; /* 隐藏滚动条 */
      }

      -ms-overflow-style: none; /* 适用于 IE 和 Edge */
      scrollbar-width: none; /* 适用于 Firefox */

      .imageBox {
        width: 292px;
        height: 168px;
        cursor: pointer;
        overflow: hidden;
        position: relative;
        border-radius: 8px;
        margin: 10px 20px;
        background: #ffffff;
        box-shadow: 4px 2px 24px 0px rgba(123, 123, 123, 0.1);

        .img {
          width: 284px;
          height: 160px;
          margin: 2px 0px;
          border-radius: 8px;
        }

        &:hover {
          border: 1px solid #1777ff;
        }

        &.selected {
          border: 2px solid #1777ff;
        }

        &.disabled {
          cursor: not-allowed;
        }
      }
    }
  }
</style>
