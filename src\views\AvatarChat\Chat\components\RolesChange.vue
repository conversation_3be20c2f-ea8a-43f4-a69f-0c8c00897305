<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { message } from 'ant-design-vue';
  // import { RightOutlined } from '@ant-design/icons-vue';
  import { getLocalItem } from '@/utils/common';
  // import AvatarIcon from '@/assets/image/base/pictures/avatar.png';
  import EmptyPerson from '@/assets/image/base/pictures/empty_project.png';
  import AVATAR01 from '@/assets/image/avatar/2d.png';
  import AVATAR02 from '@/assets/image/avatar/3d.png';

  import Loading from '../../Loading/index.vue';
  import { strtPretrain, deleteMode } from '@/api/avatarChat';
  import { getDigitalHumans, reGenerateDigitalHumans, delDigitalHumans } from '@/api/videoGeneratedDigitalHumans';

  import CreateDigitalHumanModal from '../../Create/index.vue';
  import ModeSelectModal from '../../Create/ModeSelectModal.vue';
  import VideoModal from '../../Create/VideoModal.vue';

  interface PretrainItemProps {
    id: string | undefined;
    avatar_id: string;
    image_url: string;
    name: string;
    pretrain_status?: number;
    error_msg?: string;
    audio_url?: string;
    video_url?: string; // Add this property
    source_image_url?: string;
    default_speaker?: string;
    category?: string;
    target_path?: string;
    gender?: string;
    is_cartoon?: boolean;
    user_id?: string;
    original_video_url?: string;
    preview_url?: string;
  }

  // Props
  const props = defineProps({
    isCollapsed: Boolean,
    config: Object,
    setConfig: Function,
    selectedRole: String,
    selectedRoleIndex: Number,
    selectedRoleSource: String,
  });

  const emit = defineEmits(['role-selected']);

  const defaultCharacterLibrary = [
    {
      id: 0,
      created_at: '2025-03-27T14:40:39.184567+08:00',
      updated_at: '2025-03-27T14:46:07.849233+08:00',
      deleted_at: null,
      is_activated: true,
      image_url: '@/assets/image/avatar/2d.png',
      video_url: '',
      user_id: 490189310428057600,
      pretrain_status: 1,
      preload_status: 2,
      avatar_id: '5rRekX4u0M',
      source_image_url: '',
      name: '职业女性',
      gender: 'woman',
      error_msg: null,
      // disabled: true, // 添加禁用的标记
    },
    // {
    //   id: 1,
    //   created_at: '2025-03-27T10:03:44.068392+08:00',
    //   updated_at: '2025-03-27T10:03:49.288088+08:00',
    //   deleted_at: null,
    //   is_activated: true,
    //   image_url: ' @/assets/image/avatar/3d.png',
    //   video_url: '',
    //   user_id: 490189310428057600,
    //   pretrain_status: 0,
    //   preload_status: 0,
    //   avatar_id: '5Dnw5AKqkj',
    //   source_image_url: '',
    //   name: '职业女性',
    //   gender: 'man',
    //   error_msg: null,
    // },
  ]; // Reactive state
  const activeTab = ref('1'); // 当前激活的 Tab
  const characterLibrary = ref(defaultCharacterLibrary); // 人物库数据
  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');
  const pretrainList = ref<PretrainItemProps[]>([]); // 预训练列表
  const videoPretrainList = ref<PretrainItemProps[]>([]); // 视频预训练列表
  const selectedRoleObj = ref<any>(null); // 当前选中的角色对象
  const selectedSource = ref<'library' | 'my' | null>(null); // 当前选中来源
  const selectedIndex = ref<number | null>(null); // 当前选中的索引
  const createModalRef = ref(); // 模态框引用
  const modeSelectVisible = ref(false); // 模式选择弹窗显示状态
  const videoModalRef = ref(); // 视频模态框引用

  // 轮询相关状态
  const pollingInterval = ref<NodeJS.Timeout | null>(null);
  const isPolling = ref(false);
  const pollingStartTime = ref<number | null>(null);
  const maxPollingDuration = 15 * 60 * 1000; // 15分钟，单位毫秒

  // 获取预训练列表
  const getList = async () => {
    try {
      const data = await getDigitalHumans({
        user_id: userId,
      });
      pretrainList.value = data.video_avatars.concat(data.user_faces) || [];
      videoPretrainList.value = data.video_avatars || [];

      // 检查是否需要停止轮询
      if (isPolling.value) {
        handlePollingComplete();
      }
    } catch (error) {
      console.log('获取列表失败', error);
      message.error('获取列表失败');
    }
  };

  // 开始轮询
  const startPolling = () => {
    if (isPolling.value) return; // 避免重复启动

    isPolling.value = true;
    pollingStartTime.value = Date.now(); // 记录开始时间
    console.log('开始轮询数字人列表...');

    // 立即获取一次数据
    getList();

    // 设置定时轮询，每3秒检查一次
    pollingInterval.value = setInterval(async () => {
      // 检查是否超过最大轮询时间
      if (pollingStartTime.value && Date.now() - pollingStartTime.value >= maxPollingDuration) {
        stopPolling();
        console.log('轮询已超过15分钟，自动停止轮询');
        return;
      }

      await getList();
    }, 3000);
  };

  // 停止轮询
  const stopPolling = () => {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value);
      pollingInterval.value = null;
    }
    isPolling.value = false;
    pollingStartTime.value = null; // 重置开始时间
    console.log('停止轮询数字人列表');
  };

  // 处理轮询完成（当所有数字人都生成完成时）
  const handlePollingComplete = () => {
    if (!pollingStartTime.value) return; // 如果没有开始时间，则不处理

    const elapsedTime = Date.now() - pollingStartTime.value;
    if (elapsedTime >= maxPollingDuration) {
      stopPolling();
      console.log('轮询已超过15分钟，停止轮询');
    } else {
      // 检查是否还有正在训练中的数字人
      const hasTrainingItems = pretrainList.value.some((item) => item.pretrain_status === 2);

      if (!hasTrainingItems) {
        stopPolling();
        console.log('所有数字人生成完成，停止轮询');
      }
    }
  };

  // 切换 Tab
  const handleTabChange = (key: string) => {
    activeTab.value = key;
    getList();
  };

  // 打开模态框 - 先显示模式选择
  const handleOpenModal = () => {
    console.log('打开模式选择弹窗');
    modeSelectVisible.value = true;
  };

  // 关闭模式选择弹窗
  const handleModeSelectClose = () => {
    modeSelectVisible.value = false;
  };

  // 处理模式选择
  const handleModeSelect = (mode: 'video' | 'image') => {
    modeSelectVisible.value = false;
    if (mode === 'video') {
      // 打开视频生成数字人模态框
      console.log('打开视频生成数字人模态框', videoModalRef.value);
      videoModalRef.value?.openModal();
    } else {
      // 打开变装数字人模态框
      console.log('打开变装数字人模态框', createModalRef.value);
      createModalRef.value?.openModal();
    }
  };

  // 关闭模态框
  const handleCloseModal = () => {
    activeTab.value = '2';
    getList();
    // 启动轮询，监控数字人生成状态
    startPolling();
  };

  // 选择人物库中的角色
  const handleCharacterSelect = (index: number) => {
    selectedSource.value = 'library';
    selectedIndex.value = index;
    selectedRoleObj.value = characterLibrary.value[index];
    // emit('role-selected', { role: selectedRoleObj.value, source: selectedSource.value });
    emit('role-selected', { role: characterLibrary.value[index], source: 'library', index });
  };

  // 选择我的人物中的角色
  const handlePretrainSelect = (index: number) => {
    selectedSource.value = 'my';
    selectedIndex.value = index;
    selectedRoleObj.value = pretrainList.value[index];
    emit('role-selected', { role: pretrainList.value[index], source: 'my', index });
    // emit('role-selected', { role: selectedRoleObj.value, source: selectedSource.value });
  };

  // 获取预训练列表时调用
  onMounted(() => {
    // getList().then(() => {
    //   selectedSource.value = 'my';
    //   selectedIndex.value = 0;
    //   selectedRoleObj.value = pretrainList.value[0];
    //   emit('role-selected', selectedRoleObj.value);
    // });
    getList();
    // 默认选中人物库第一个
    selectedSource.value = 'library';
    selectedIndex.value = 0;
    selectedRoleObj.value = characterLibrary.value[0];
    emit('role-selected', selectedRoleObj.value);
  });

  // 组件卸载时清理轮询定时器
  onUnmounted(() => {
    stopPolling();
  });

  // 删除数字人
  const handleDelete = async (item: PretrainItemProps) => {
    try {
      // 判断当前item是来自video_avatars还是user_faces
      const isFromVideoAvatars = videoPretrainList.value.some((videoItem) => videoItem.avatar_id === item.avatar_id);

      if (isFromVideoAvatars) {
        // 如果是video_avatars中的数据，使用delDigitalHumans接口
        await delDigitalHumans({
          avatar_id: item.avatar_id,
          user_id: userId,
        });
      } else {
        // 如果是user_faces中的数据，使用deleteMode接口
        await deleteMode({
          ids: [item.id],
        });
      }
      getList();
    } catch (error) {
      console.log('删除数字人失败', error);
    }
  };

  // 重新生成数字人
  const handleReGenerate = async (item: PretrainItemProps) => {
    // console.log('重新生成数字人', item);

    // pageLoading.value = true;
    // const isCartoon = selectedDigitalMan.value.index === digitalList.value.length - 1;
    // 判断当前item是来自video_avatars还是user_faces
    const isFromVideoAvatars = videoPretrainList.value.some((videoItem) => videoItem.avatar_id === item.avatar_id);

    if (isFromVideoAvatars) {
      // 如果是video_avatars中的数据，使用delDigitalHumans接口
      await reGenerateDigitalHumans({
        avatar_id: item.avatar_id,
        user_id: userId,
      });
    } else {
      // 如果是user_faces中的数据，使用deleteMode接口
      await strtPretrain({
        // ...item
        re_pretrain: true,
        source_path: item.source_image_url,
        category: item.category || 'fast',
        is_cartoon: item.is_cartoon || false,
        user_id: item.user_id,
        image_url: item.image_url,
        target_path: item.target_path,
        name: item.name,
        gender: item.gender,
        speaker: item.default_speaker,
        id: item.id,
      }).then(() => {
        // props.handleCloseModal();
        // isOpenModal.value = false;
        // message.success('重新开始训练');
      });
      // .catch(() => {
      //   message.error('训练失败');
      // })
      // .finally(() => {
      //   pageLoading.value = false;
      // });
    }

    setTimeout(() => {
      getList();
    }, 1000);
  };
</script>

<template>
  <div class="rolesContainer" :style="{ overflow: isCollapsed ? 'hidden' : '324px' }">
    <!-- <div class="createRole">
      <a-image class="avatar" :src="AvatarIcon" :preview="false" />
      <div class="createRole_text" @click="handleOpenModal">
        <span>创建数字人</span>
        <RightOutlined />
      </div>
    </div> -->
    <div class="tab-buttons">
      <div class="tab-button" :class="{ active: activeTab === '1' }" @click="() => handleTabChange('1')">
        公共数字人
      </div>
      <div class="tab-button" :class="{ active: activeTab === '2' }" @click="() => handleTabChange('2')">
        我的数字人
      </div>
    </div>
    <div class="hidden_sider_container">
      <template v-if="activeTab === '1'">
        <div
          v-for="(item, index) in characterLibrary"
          :key="index"
          class="imageBox"
          :class="{
            selected: selectedRoleSource === 'library' && selectedRoleIndex === index,
            // disabled: item.disabled,
          }"
          @click="handleCharacterSelect(index)"
        >
          <img v-if="index === 0" class="img" :src="AVATAR01" :preview="false" alt="静态图片" />
          <img v-if="index === 1" class="img" :src="AVATAR02" :preview="false" alt="静态图片" />
          <a-tooltip placement="bottom" title="职场女性">
            <span class="roleName">{{ item.name }}</span>
          </a-tooltip>
        </div>
      </template>
      <template v-else>
        <div v-if="pretrainList.length === 0" class="noData">
          <a-image :src="EmptyPerson" :preview="false" />
          <p class="noData_text">你还未创建数字人，快去创建吧</p>
          <a-button type="primary" ghost class="createRole_btn" @click="handleOpenModal">
            <span style="color: #636466; font-size: 14px">创建数字人</span>
          </a-button>
        </div>
        <template v-else>
          <div class="createRole">
            <!-- <a-image class="avatar" :src="AvatarIcon" :preview="false" /> -->
            <div class="createRole_text" @click="handleOpenModal">
              <span style="width: 20px; height: 20px; font-size: 20px; font-weight: 600"> + </span>
              <span>创建数字人</span>
              <!-- <RightOutlined /> -->
            </div>
          </div>
          <div
            v-for="(item, index) in pretrainList"
            :key="item.avatar_id"
            class="imageBox"
            :class="{ selected: selectedRoleSource === 'my' && selectedRoleIndex === index }"
            @click="handlePretrainSelect(index)"
          >
            <img class="img" :src="item.image_url || item.preview_url" alt="" :preview="false" />
            <a-tooltip placement="bottom" :title="item.name">
              <span class="roleName">{{ item.name }}</span>
            </a-tooltip>
            <Loading
              v-if="item.pretrain_status === 0"
              state="数字人生成失败，请重新生成"
              :name="item.name"
              :handle-re-generate="() => handleReGenerate(item)"
              :handle-delete="() => item.id && handleDelete(item)"
              from="rolesChange"
            />
            <Loading v-else-if="item.pretrain_status === 2" state="正在训练中..." from="rolesChange" />
          </div>
        </template>
      </template>
    </div>
    <CreateDigitalHumanModal ref="createModalRef" :handle-close-modal="handleCloseModal" />
    <ModeSelectModal :visible="modeSelectVisible" @close="handleModeSelectClose" @select="handleModeSelect" />
    <VideoModal ref="videoModalRef" :handle-close-modal="handleCloseModal" />
  </div>
</template>

<style lang="less" scoped>
  .rolesContainer {
    width: 100%;
    height: 100%; /* 改为100%占满父容器高度 */
    display: flex; /* 添加flex布局 */
    flex-direction: column; /* 垂直排列 */

    .createRole {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 280px;
      height: 40px;
      margin: 5px 25px 5px 15px;
      border-radius: 4px;
      border: 1px dashed #dce0e6;
      z-index: 10;

      &:hover {
        border: 1px solid #1777ff;
      }

      :deep(.ant-image-img) {
        width: 36px;
        height: 36px;
        margin: 12px 0 12px 20px;
      }

      .createRole_text {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        span {
          font-size: 16px;
          // width: 70px;
          height: 20px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #636466;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }
      }
    }

    .hidden_sider_container {
      display: flex;
      justify-content: flex-start;
      border-radius: 8px;
      flex-wrap: wrap;
      flex: 1; /* 占满剩余空间 */
      min-height: 0; /* 允许flex子项收缩 */
      overflow-y: auto; /* 启用纵向滚动 */
      overflow-x: hidden; /* 禁用横向滚动 */
      padding-left: 10px;
      align-content: flex-start; /* 内容从顶部开始排列 */
      max-height: 585px;

      ::-webkit-scrollbar {
        display: none; /* 隐藏滚动条 */
      }

      -ms-overflow-style: none; /* 适用于 IE 和 Edge */
      scrollbar-width: none; /* 适用于 Firefox */

      .noData {
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        width: 100%;
        height: 500px;
        margin-right: 10px;
        text-align: center;

        .noData_text {
          margin-top: 8px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #969799;
          line-height: 20px;
          text-align: right;
          font-style: normal;
        }

        .createRole_btn {
          border: 1px solid rgba(0, 0, 0, 0.15);
        }
        .createRole_btn:hover {
          border: 1px solid #1777ff;
        }
        .createRole_btn:hover span {
          color: #1777ff !important;
        }
      }

      .imageBox {
        width: 132px;
        height: 176px;
        cursor: pointer;
        overflow: hidden;
        position: relative;
        border-radius: 8px;
        margin: 10px;
        background: linear-gradient(135deg, #fafafa 0%, #e6e6e6 100%);
        border: 2px solid transparent;

        &:hover {
          border: 1px solid #1777ff;
        }

        &.selected {
          border: 2px solid #1777ff;
        }

        .img {
          padding-left: 20px;
          width: 108px;
          height: 161px;
          object-fit: cover;
        }

        .roleName {
          position: absolute;
          bottom: -2px;
          width: 140px;
          height: 28px;
          background: rgba(226, 233, 242, 0.6);
          border-radius: 0px 0px 4px 4px;
          z-index: 10;
          display: flex;
          justify-content: center;
          align-items: center;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          // margin-left: 6px;
        }
      }
    }

    .tab-buttons {
      display: flex;
      // justify-content: space-between;
      // width: 284px;
      height: 32px;
      margin: 10px 20px;
      border-radius: 8px;
      // background: #f2f2f2;
    }

    .tab-button {
      display: flex;
      // width: 140px;
      height: 32px;

      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;

      text-align: center;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      color: #939599;
      margin: 2px;
      padding: 6px 16px;
      border-radius: 8px;

      &:not(:last-child) {
        border-right: none;
      }
      &.active {
        background: #f2f8ff;
        // color: #000000;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #17181a !important;
        line-height: 20px;
        text-align: right;
        font-style: normal;
      }
      &:hover {
        color: #17181a;
      }
    }

    // :deep(.loading) {
    //   .fail {
    //     .fail-icon {
    //       width: 32px;
    //       // padding-bottom: 10px;
    //       // margin-bottom: 10px;
    //     }
    //     .fail-text {
    //       font-size: 9px;
    //       margin-top: 10px;
    //     }
    //     .fail-btn {
    //       padding: 0 10px;
    //       margin: 0;
    //       width: 100%;
    //       display: flex;
    //       justify-content: space-around;
    //       font-size: 10px;
    //       .btn-item {
    //         padding: 2px;
    //         // margin: 0;
    //         // margin-right: 5px;
    //         font-size: 10px;

    //         // :deep(.ant-btn-default) {
    //         //   background: #999999;
    //         //   color: #fff;
    //         // }
    //       }
    //       .fail-btn-item {
    //         padding: 2px 10px;

    //         font-size: 10px;
    //       }
    //     }
    //   }
    // }

    :deep(.ant-popconfirm-message-icon) {
      display: none;
    }
  }
</style>
