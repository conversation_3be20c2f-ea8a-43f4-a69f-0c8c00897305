<script setup lang="ts">
  import { ref, onMounted, onUnmounted, computed } from 'vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { strtPretrain, deleteMode, reName } from '@/api/avatarChat';
  import {
    getDigitalHumans,
    reGenerateDigitalHumans,
    reNameDigitalHumans,
    delDigitalHumans,
  } from '@/api/videoGeneratedDigitalHumans';
  import { getLocalItem } from '@/utils/common';
  import Loading from '../Loading/index.vue';
  import Icon from '@/components/Icon/index.vue';
  import EmptyImage from '@/assets/image/base/pictures/empty_project.png';
  import AVATAR01 from '@/assets/image/avatar/2d.png';
  import CreateDigitalHumanModal from '../Create/index.vue';
  import ModeSelectModal from '../Create/ModeSelectModal.vue';
  import VideoGenerationModal from '../Create/VideoModal.vue';

  const defaultCharacterLibrary = [
    {
      id: 0,
      created_at: '2025-03-27T14:40:39.184567+08:00',
      updated_at: '2025-03-27T14:46:07.849233+08:00',
      deleted_at: null,
      is_activated: true,
      image_url: '@/assets/image/avatar/2d.png',
      video_url: '',
      user_id: 490189310428057600,
      pretrain_status: 1,
      preload_status: 2,
      avatar_id: '5rRekX4u0M',
      source_image_url: '',
      name: '职业女性',
      gender: 'woman',
      error_msg: null,
    },
  ];

  const characterLibrary = ref(defaultCharacterLibrary);
  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');

  // 分别存储不同类型的数字人列表
  const videoPretrainList = ref<PretrainItemProps[]>([]); // 视频预训练列表
  const userFacesPretrainList = ref<PretrainItemProps[]>([]); // 用户人脸预训练列表

  // 数据加载状态
  const loadingStates = ref({
    video: false,
    faceswap: false,
    all: false,
  });

  // 数据缓存状态 - 记录哪些类型的数据已经加载过
  const dataCacheStatus = ref({
    video: false,
    faceswap: false,
    all: false,
  });

  // 根据选中的筛选条件计算当前显示列表（按创建时间倒序）
  const sortByCreatedAtDesc = (list: Array<PretrainItemProps>): Array<PretrainItemProps> => {
    return [...list].sort((a, b) => {
      const aTime = a.created_at ? new Date(a.created_at).getTime() : 0;
      const bTime = b.created_at ? new Date(b.created_at).getTime() : 0;
      return bTime - aTime;
    });
  };

  const pretrainList = computed(() => {
    switch (selectedFilter.value) {
      case '视频生成':
        return sortByCreatedAtDesc(videoPretrainList.value);
      case '变装生成':
        return sortByCreatedAtDesc(userFacesPretrainList.value);
      case '全部':
      default:
        return sortByCreatedAtDesc([...videoPretrainList.value, ...userFacesPretrainList.value]);
    }
  });

  const createModalRef = ref(); // 模态框引用

  // hoverVideoId 用于追踪当前hover的视频avatar_id
  const hoverVideoId = ref<string | null>(null);

  // video-modal控制
  const videoModalVisible = ref(false);
  const videoModalUrl = ref('');
  const videoModalName = ref('');

  // 播放成功的数字人视频
  const openSuccessVideo = ref<boolean>(false);

  const personName = ref('');
  const openNameModal = ref(false);
  const currentRenamingItem = ref<PretrainItemProps | null>(null);
  const selectedDigitalHuman = ref<string | null>(null);
  const audioPlayer = ref<HTMLAudioElement | null>(null);

  // 选中的筛选按钮
  const selectedFilter = ref<string>('全部');

  // 轮询相关状态
  const pollingInterval = ref<NodeJS.Timeout | null>(null);
  const isPolling = ref(false);
  const pollingStartTime = ref<number | null>(null);
  const maxPollingDuration = 15 * 60 * 1000; // 15分钟，单位毫秒
  // 连续无训练计数，避免偶发空窗导致过早停止
  const noTrainingConsecutiveCount = ref(0);

  // 全局轮询状态 - 记录是否有任何数字人正在训练中
  const globalTrainingStatus = ref({
    hasVideoTraining: false,
    hasFaceswapTraining: false,
    lastCheckTime: 0,
  });

  // 更新全局训练状态
  const updateGlobalTrainingStatus = () => {
    globalTrainingStatus.value.hasVideoTraining = videoPretrainList.value.some((item) => item.pretrain_status === 2);
    globalTrainingStatus.value.hasFaceswapTraining = userFacesPretrainList.value.some(
      (item) => item.pretrain_status === 2,
    );
    globalTrainingStatus.value.lastCheckTime = Date.now();

    console.log('全局训练状态更新:', {
      video: globalTrainingStatus.value.hasVideoTraining,
      faceswap: globalTrainingStatus.value.hasFaceswapTraining,
      time: new Date().toLocaleTimeString(),
    });
  };

  // 检查是否应该继续轮询
  const shouldContinuePolling = (): boolean => {
    // 更新全局训练状态
    updateGlobalTrainingStatus();

    // 如果任何页签有训练中的数字人，就应该继续轮询
    return globalTrainingStatus.value.hasVideoTraining || globalTrainingStatus.value.hasFaceswapTraining;
  };

  // 开始轮询
  const startPolling = () => {
    if (isPolling.value) return; // 避免重复启动

    isPolling.value = true;
    pollingStartTime.value = Date.now(); // 记录开始时间
    console.log('开始轮询数字人列表...');
    noTrainingConsecutiveCount.value = 0;

    // 立即获取一次数据（轮询期间始终刷新全部数据，确保两类数字人状态同步更新）
    getAllDigitalHumans(true);

    // 设置定时轮询，每3秒检查一次
    pollingInterval.value = setInterval(async () => {
      // 检查是否超过最大轮询时间
      if (pollingStartTime.value && Date.now() - pollingStartTime.value >= maxPollingDuration) {
        stopPolling();
        console.log('轮询已超过15分钟，自动停止轮询');
        return;
      }

      // 轮询时始终刷新全部数据，避免仅当前页签造成另一类不更新
      await getAllDigitalHumans(true);

      // 检查是否应该继续轮询（需要连续多次无训练才停止，防止后端短暂未返回训练中状态）
      if (!shouldContinuePolling()) {
        noTrainingConsecutiveCount.value += 1;
        if (noTrainingConsecutiveCount.value >= 3) {
          stopPolling();
          console.log('连续3次未检测到训练，停止轮询');
        }
      } else {
        noTrainingConsecutiveCount.value = 0;
      }
    }, 3000);
  };

  // 停止轮询
  const stopPolling = () => {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value);
      pollingInterval.value = null;
    }
    isPolling.value = false;
    pollingStartTime.value = null; // 重置开始时间
    noTrainingConsecutiveCount.value = 0;
    console.log('停止轮询数字人列表');
  };

  // 处理轮询完成（当所有数字人都生成完成时）
  const handlePollingComplete = () => {
    if (!pollingStartTime.value) return; // 如果没有开始时间，则不处理

    const elapsedTime = Date.now() - pollingStartTime.value;
    if (elapsedTime >= maxPollingDuration) {
      stopPolling();
      console.log('轮询已超过15分钟，停止轮询');
    } else {
      // 检查是否应该继续轮询
      if (!shouldContinuePolling()) {
        stopPolling();
        console.log('所有数字人生成完成，停止轮询');
      }
    }
  };

  // 检查其他页签是否还有训练中的数字人
  const checkOtherTabsForTraining = (): boolean => {
    // 检查视频生成页签
    const hasVideoTraining = videoPretrainList.value.some((item) => item.pretrain_status === 2);
    // 检查变装生成页签
    const hasFaceswapTraining = userFacesPretrainList.value.some((item) => item.pretrain_status === 2);

    return hasVideoTraining || hasFaceswapTraining;
  };

  interface PretrainItemProps {
    id: string | undefined;
    avatar_id: string;
    image_url: string;
    name: string;
    pretrain_status?: number;
    error_msg?: string;
    audio_url?: string;
    video_url?: string; // Add this property
    source_image_url?: string;
    default_speaker?: string;
    category?: string;
    target_path?: string;
    gender?: string;
    is_cartoon?: boolean;
    user_id?: string;
    original_video_url?: string;
    preview_url?: string;
  }

  // 获取视频生成数字人列表
  const getVideoDigitalHumans = async (forceRefresh = false) => {
    // 如果已经有缓存且不是强制刷新，则跳过
    if (dataCacheStatus.value.video && !forceRefresh) {
      return;
    }

    if (loadingStates.value.video) return; // 避免重复请求

    loadingStates.value.video = true;
    try {
      const data = await getDigitalHumans({
        user_id: userId,
        type: 'video',
      });
      videoPretrainList.value = data.video_avatars || [];
      dataCacheStatus.value.video = true;
    } catch (error) {
      console.log('获取视频生成数字人列表失败', error);
      message.error('获取视频生成数字人列表失败');
    } finally {
      loadingStates.value.video = false;
    }
  };

  // 获取变装生成数字人列表
  const getUserFacesDigitalHumans = async (forceRefresh = false) => {
    // 如果已经有缓存且不是强制刷新，则跳过
    if (dataCacheStatus.value.faceswap && !forceRefresh) {
      return;
    }

    if (loadingStates.value.faceswap) return; // 避免重复请求

    loadingStates.value.faceswap = true;
    try {
      const data = await getDigitalHumans({
        user_id: userId,
        type: 'faceswap',
      });
      userFacesPretrainList.value = data.user_faces || [];
      dataCacheStatus.value.faceswap = true;
    } catch (error) {
      console.log('获取变装生成数字人列表失败', error);
      message.error('获取变装生成数字人列表失败');
    } finally {
      loadingStates.value.faceswap = false;
    }
  };

  // 获取所有数字人列表
  const getAllDigitalHumans = async (forceRefresh = false) => {
    // 如果已经有缓存且不是强制刷新，则跳过
    if (dataCacheStatus.value.all && !forceRefresh) {
      return;
    }

    if (loadingStates.value.all) return; // 避免重复请求

    loadingStates.value.all = true;
    try {
      const data = await getDigitalHumans({
        user_id: userId,
      });
      videoPretrainList.value = data.video_avatars || [];
      userFacesPretrainList.value = data.user_faces || [];
      dataCacheStatus.value.all = true;
      // 同时标记子类型为已加载
      dataCacheStatus.value.video = true;
      dataCacheStatus.value.faceswap = true;
    } catch (error) {
      console.log('获取数字人列表失败', error);
      message.error('获取数字人列表失败');
    } finally {
      loadingStates.value.all = false;
    }
  };

  // 获取预训练列表 - 根据当前筛选条件获取对应数据
  const getList = async (forceRefresh = false) => {
    try {
      // 根据当前筛选条件决定获取哪些数据
      if (selectedFilter.value === '视频生成') {
        await getVideoDigitalHumans(forceRefresh);
      } else if (selectedFilter.value === '变装生成') {
        await getUserFacesDigitalHumans(forceRefresh);
      } else {
        // 全部：获取所有数据
        await getAllDigitalHumans(forceRefresh);
      }

      // 检查是否需要停止轮询
      if (isPolling.value) {
        handlePollingComplete();
      }
    } catch (error) {
      console.log('获取列表失败', error);
      message.error('获取列表失败');
    }
  };

  // 智能数据预加载 - 预加载其他类型的数据以提高用户体验
  const preloadOtherData = async () => {
    // 如果当前是"全部"标签，预加载其他类型数据
    if (selectedFilter.value === '全部') {
      // 并行预加载，不阻塞当前操作
      Promise.all([getVideoDigitalHumans(), getUserFacesDigitalHumans()]).catch(() => {
        // 预加载失败不影响主流程
      });
    }
  };

  // 处理筛选按钮点击 - 现在只需要切换显示，不需要重新筛选数据
  const handleFilterClick = async (filter: string) => {
    selectedFilter.value = filter;

    // 如果切换到某个特定类型，且该类型数据还未加载，则加载数据
    if (filter === '视频生成' && !dataCacheStatus.value.video) {
      await getVideoDigitalHumans();
    } else if (filter === '变装生成' && !dataCacheStatus.value.faceswap) {
      await getUserFacesDigitalHumans();
    }

    // 预加载其他数据
    preloadOtherData();

    // 如果当前正在轮询，检查是否需要继续轮询
    if (isPolling.value) {
      // 切换页签后，检查是否应该继续轮询
      if (!shouldContinuePolling()) {
        stopPolling();
        console.log('切换页签后，所有数字人都已完成，停止轮询');
      }
    }
  };

  const deletePopoverOpenId = ref<string | null>(null);
  function openDeletePopover(id: string) {
    deletePopoverOpenId.value = id;
  }
  function closeDeletePopover() {
    deletePopoverOpenId.value = null;
  }

  // 刷新数据缓存状态 - 当数据发生变化时调用
  const refreshDataCache = () => {
    // 重置缓存状态，下次获取数据时会重新加载
    dataCacheStatus.value = {
      video: false,
      faceswap: false,
      all: false,
    };
  };

  // 获取预训练列表时调用
  onMounted(() => {
    getList();
  });

  // 组件卸载时清理轮询定时器
  onUnmounted(() => {
    stopPolling();
  });

  // 删除数字人
  const handleDelete = async (item: PretrainItemProps) => {
    try {
      // 判断当前item是来自video_avatars还是user_faces
      const isFromVideoAvatars = videoPretrainList.value.some((videoItem) => videoItem.avatar_id === item.avatar_id);

      if (isFromVideoAvatars) {
        // 如果是video_avatars中的数据，使用delDigitalHumans接口
        await delDigitalHumans({
          avatar_id: item.avatar_id,
          user_id: userId,
        });
      } else {
        // 如果是user_faces中的数据，使用deleteMode接口
        await deleteMode({
          ids: [item.id],
        });
      }

      // 删除后刷新缓存状态，确保下次获取数据时是最新的
      refreshDataCache();
      getList(true);
    } catch (error) {
      console.log('删除数字人失败', error);
    }
  };

  // 重新生成数字人
  const handleReGenerate = async (item: PretrainItemProps) => {
    // 判断当前item是来自video_avatars还是user_faces
    const isFromVideoAvatars = videoPretrainList.value.some((videoItem) => videoItem.avatar_id === item.avatar_id);

    if (isFromVideoAvatars) {
      // 如果是video_avatars中的数据，使用delDigitalHumans接口
      await reGenerateDigitalHumans({
        avatar_id: item.avatar_id,
        user_id: userId,
      });
    } else {
      // 如果是user_faces中的数据，使用deleteMode接口
      await strtPretrain({
        // ...item
        re_pretrain: true,
        source_path: item.source_image_url,
        category: item.category || 'fast',
        is_cartoon: item.is_cartoon || false,
        user_id: item.user_id,
        image_url: item.image_url,
        target_path: item.target_path,
        name: item.name,
        gender: item.gender,
        speaker: item.default_speaker,
        id: item.id,
      }).then(() => {
        // props.handleCloseModal();
        // isOpenModal.value = false;
        // message.success('重新开始训练');
      });
    }

    // 重新生成后刷新缓存状态
    refreshDataCache();
    setTimeout(() => {
      getList(true);
    }, 1000);
  };

  //reName
  const handleReName = async (item: PretrainItemProps) => {
    if (!personName.value) {
      message.error('请输入');
      return;
    }
    try {
      // 判断当前item是来自video_avatars还是user_faces
      const isFromVideoAvatars = videoPretrainList.value.some((videoItem) => videoItem.avatar_id === item.avatar_id);

      if (isFromVideoAvatars) {
        // 如果是video_avatars中的数据，使用delDigitalHumans接口
        await reNameDigitalHumans({
          id: item.id,
          name: personName.value,
        });
      } else {
        // 如果是user_faces中的数据，使用deleteMode接口
        await reName({ id: item.id, name: personName.value });
      }

      openNameModal.value = false;
      personName.value = '';
      currentRenamingItem.value = null;

      // 重命名后刷新缓存状态
      refreshDataCache();
      getList(true);
    } catch (error) {
      openNameModal.value = false;
      personName.value = '';
      currentRenamingItem.value = null;
      console.log('重命名失败', error);
    }
  };

  // 打开重命名模态框
  const handleOpenRenameModal = (item: PretrainItemProps) => {
    personName.value = item.name;
    openNameModal.value = true;
    currentRenamingItem.value = item;
  };

  // 处理数字人选择
  const handleDigitalHumanSelect = (item: PretrainItemProps) => {
    // 选中当前数字人
    selectedDigitalHuman.value = item.avatar_id;

    // 播放音频（这里需要根据实际API调整）
    playAudio(item);
  };

  // 播放音频
  const playAudio = (item: PretrainItemProps) => {
    // 停止当前播放的音频
    if (audioPlayer.value) {
      audioPlayer.value.pause();
      audioPlayer.value = null;
    }

    // 创建新的音频播放器
    audioPlayer.value = new Audio();

    // 这里需要根据实际的音频URL来设置
    // 假设音频URL存储在item.audio_url中，如果没有这个字段需要根据实际API调整
    if (item.audio_url) {
      audioPlayer.value.src = item.audio_url;
      audioPlayer.value.play().catch((error) => {
        console.log('音频播放失败:', error);
      });
    } else {
      // 如果没有音频URL，可以播放默认音频或显示提示
      console.log('该数字人暂无音频');
    }
  };

  // 新增弹窗控制
  const modeSelectVisible = ref(false);

  const handleOpenModal = () => {
    modeSelectVisible.value = true;
  };

  // 记录最近一次创建的数字人类型：'video' | 'faceswap'
  const lastCreatedType = ref<'video' | 'faceswap' | null>(null);

  const handleModeSelect = (mode: string) => {
    modeSelectVisible.value = false;
    if (mode === 'image') {
      lastCreatedType.value = 'faceswap';
      createModalRef.value?.openModal();
    } else if (mode === 'video') {
      lastCreatedType.value = 'video';
      videoModalVisible.value = true;
    }
  };

  const handleModeSelectClose = () => {
    modeSelectVisible.value = false;
  };

  const handleCloseModal = () => {
    // 创建完成后刷新缓存状态
    refreshDataCache();
    getList(true);
    // 启动轮询，监控数字人生成状态
    if (selectedFilter.value !== '全部' && lastCreatedType.value) {
      const targetTab = lastCreatedType.value === 'video' ? '视频生成' : '变装生成';
      handleFilterClick(targetTab);
    }
    lastCreatedType.value = null;
    startPolling();
  };

  // 处理播放数字人视频（用于已创建成功的数字人）
  const handleVideoModal = (videoUrl: string, name: string) => {
    videoModalUrl.value = videoUrl;
    videoModalName.value = name;
    openSuccessVideo.value = true; // 使用 openSuccessVideo 控制播放弹窗
  };

  // 关闭视频生成数字人的创建弹窗
  const handleVideoModalClose = () => {
    videoModalVisible.value = false;
    // 创建完成后刷新缓存状态
    refreshDataCache();
    getList(true); // 创建完成后刷新列表
    // 启动轮询，监控数字人生成状态
    if (selectedFilter.value !== '全部' && lastCreatedType.value) {
      const targetTab = lastCreatedType.value === 'video' ? '视频生成' : '变装生成';
      handleFilterClick(targetTab);
    }
    lastCreatedType.value = null;
    startPolling();
  };

  // 关闭播放成功数字人视频的弹窗
  const handleOpenSuccessModal = () => {
    openSuccessVideo.value = false;
    videoModalName.value = '';
    videoModalUrl.value = '';
  };

  // 添加一个通用的数据刷新函数，用于外部调用
  const refreshAllData = () => {
    refreshDataCache();
    getList(true);
  };

  // 暴露给父组件的方法
  defineExpose({
    refreshAllData,
  });
</script>
<template>
  <div class="digital-image-container">
    <div class="my-digital-person-container">
      <span class="digital-person-title">我的数字人</span>
      <a-button type="primary" class="create-digital-person-btn" @click="handleOpenModal">
        <PlusOutlined />
        <span class="btn-text">创建数字人</span>
      </a-button>
      <div class="button-list">
        <span class="button-item" :class="{ selected: selectedFilter === '全部' }" @click="handleFilterClick('全部')">
          全部
        </span>
        <span
          class="button-item"
          :class="{ selected: selectedFilter === '视频生成' }"
          @click="handleFilterClick('视频生成')"
        >
          视频生成
        </span>
        <span
          class="button-item"
          :class="{ selected: selectedFilter === '变装生成' }"
          @click="handleFilterClick('变装生成')"
        >
          变装生成
        </span>
      </div>

      <template v-if="pretrainList.length > 0">
        <div class="my-digital-person">
          <div v-for="item in pretrainList" :key="item.avatar_id" class="imageBox">
            <a-popover
              :open="openNameModal && currentRenamingItem?.avatar_id === item.avatar_id"
              trigger="click"
              placement="topRight"
              @update:open="
                (visible: boolean) => {
                  if (!visible) {
                    openNameModal = false;
                    personName = '';
                    currentRenamingItem = null;
                  }
                }
              "
            >
              <template #content>
                <div style="width: 220px">
                  <div style="margin-bottom: 8px">重命名</div>
                  <a-input
                    v-model:value="personName"
                    maxlength="10"
                    placeholder="请输入形象名称，10个字内"
                    style="margin-bottom: 8px"
                  />
                  <div style="text-align: right">
                    <a-button
                      size="small"
                      style="margin-right: 8px"
                      @click="
                        () => {
                          openNameModal = false;
                          personName = '';
                          currentRenamingItem = null;
                        }
                      "
                      >取消</a-button
                    >
                    <a-button size="small" type="primary" @click="handleReName(currentRenamingItem!)">确定</a-button>
                  </div>
                </div>
              </template>
              <div>
                <template v-if="!item.video_url && item.pretrain_status !== 1">
                  <img class="img" :src="item.image_url || item.preview_url" alt="" :preview="false" />
                </template>
                <template v-else>
                  <div
                    class="video-hover-wrapper"
                    @mouseenter="hoverVideoId = item.avatar_id"
                    @mouseleave="hoverVideoId = null"
                  >
                    <video
                      class="img"
                      :src="item.video_url || item.original_video_url"
                      :poster="item.image_url || item.preview_url"
                      playsinline
                      controlsList="nodownload noplaybackrate"
                      disablePictureInPicture
                    />
                    <a-popover
                      trigger="hover"
                      placement="top"
                      :open="deletePopoverOpenId === item.avatar_id"
                      @open="openDeletePopover(item.avatar_id)"
                      @close="closeDeletePopover"
                    >
                      <template #content>
                        <div style="width: 220px">
                          <div style="font-size: 16px; color: #17181a; margin-bottom: 8px">
                            确定删除数字人"{{ item.name }}"？
                          </div>
                          <div style="color: #999; margin-bottom: 12px">删除后不可恢复</div>
                          <div style="text-align: right">
                            <a-button size="small" style="margin-right: 8px" @click="closeDeletePopover">取消</a-button>
                            <a-button
                              size="small"
                              type="primary"
                              @click="
                                item.id && handleDelete(item);
                                closeDeletePopover();
                              "
                              >确定</a-button
                            >
                          </div>
                        </div>
                      </template>
                      <a-dropdown placement="rightTop" class="more-icon">
                        <template #overlay>
                          <a-menu>
                            <a-menu-item key="1" @click="handleOpenRenameModal(item)">重命名</a-menu-item>
                            <a-menu-item key="2" @click.stop="openDeletePopover(item.avatar_id)">删除</a-menu-item>
                          </a-menu>
                        </template>
                        <a-button style="border: none">
                          <Icon name="gengduo" :size="20" class="hover-show-icon" />
                        </a-button>
                      </a-dropdown>
                    </a-popover>
                    <Icon
                      v-if="hoverVideoId === item.avatar_id"
                      name="a-bianzu2"
                      :size="32"
                      class="video-hover-icon"
                      style="pointer-events: auto; cursor: pointer"
                      @click.stop="handleVideoModal(item.video_url || item.original_video_url || '', item.name)"
                    />
                  </div>
                </template>
                <a-tooltip placement="bottom" :title="item.name">
                  <span class="roleName">{{ item.name }}</span>
                </a-tooltip>
                <Loading
                  v-if="item.pretrain_status === 0"
                  state="数字人生成失败，请重新生成"
                  :name="item.name"
                  :handle-re-generate="() => handleReGenerate(item)"
                  :handle-delete="() => item.id && handleDelete(item)"
                  from="digitalImage"
                />

                <Loading v-else-if="item.pretrain_status === 2" state="正在训练中..." from="digitalImage" />
              </div>
            </a-popover>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="card-list-empty">
          <img :src="EmptyImage" alt="" class="empty-image" />
          <template v-if="selectedFilter === '全部'">
            <span class="empty-text">你还没有创建数字人哦，快去创建吧</span>
          </template>
          <template v-else>
            <span class="flex items-center mt-12px text-[#636466] text-14px leading-22px font-400">暂无相关数字人</span>
          </template>
        </div>
      </template>
    </div>
    <div class="public-digital-person-container">
      <span class="digital-person-title">公共数字人</span>
      <div class="public-digital-person">
        <div v-for="(item, index) in characterLibrary" :key="index" class="imageBox">
          <img class="img" :src="AVATAR01" :preview="false" alt="静态图片" />
          <a-tooltip placement="bottom" title="职场女性">
            <span class="roleName">{{ item.name }}</span>
          </a-tooltip>
        </div>
      </div>
    </div>
    <a-modal
      v-model:open="openSuccessVideo"
      :title="videoModalName"
      :width="400"
      :footer="null"
      @cancel="handleOpenSuccessModal"
    >
      <video v-if="videoModalUrl" :src="videoModalUrl" controls autoplay loop style="width: 100%" />
    </a-modal>
    <VideoGenerationModal :visible="videoModalVisible" @close="handleVideoModalClose" />
    <CreateDigitalHumanModal ref="createModalRef" :handle-close-modal="handleCloseModal" />
    <ModeSelectModal :visible="modeSelectVisible" @close="handleModeSelectClose" @select="handleModeSelect" />
  </div>
</template>

<style lang="less" scoped>
  .digital-image-container {
    padding: 22px 0 0 18px;
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 20px;
    height: calc(100vh - 230px);
    overflow-y: auto;
    .my-digital-person-container {
      display: flex;
      flex-direction: column;

      .button-list {
        display: flex;
        gap: 12px;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 20px;

        .button-item {
          border-radius: 4px;
          padding: 8px 16px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #969799;
          line-height: 20px;
          text-align: center;
          font-style: normal;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            // background: #F2F8FF;
            // color: #17181A;
            opacity: 0.8;
          }

          &.selected {
            background: #f2f8ff;
            color: #17181a;
          }
        }
      }

      .create-digital-person-btn {
        width: 126px;
        height: 40px;
        background: #1777ff;
        border-radius: 8px;
        margin-bottom: 24px;

        .btn-text {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #ffffff;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }
      }

      .my-digital-person {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        justify-content: flex-start;
        gap: 20px;

        .imageBox {
          width: 180px;
          height: 238px;
          cursor: pointer;
          overflow: hidden;
          position: relative;
          border-radius: 11px;
          background: linear-gradient(135deg, #fafafa 0%, #e6e6e6 100%);
          border: 2px solid transparent;

          &:hover {
            background: #fafafa;
            box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
            // border: 1px solid #1777ff;
          }

          &.selected {
            border: 2px solid #1777ff;
          }

          .img {
            // padding-left: 20px;
            margin: 0 15px;
            width: 148px;
            height: 224px;
            object-fit: cover;
          }

          .roleName {
            position: absolute;
            bottom: -2px;
            width: 180px;
            height: 38px;
            background: rgba(226, 233, 242, 0.8);
            border-radius: 0px 0px 11px 11px;
            backdrop-filter: blur(5.43435347681893px);
            z-index: 10;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            // margin-left: 6px;
          }
        }
      }
    }

    .card-list-empty {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin: 60px;

      .empty-text {
        margin-top: 12px;
        width: 224px;
        height: 22px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #636466;
        line-height: 22px;
        text-align: right;
        font-style: normal;
      }
    }

    .public-digital-person-container {
      padding-top: 20px;
      display: flex;
      flex-direction: column;

      .public-digital-person {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        justify-content: flex-start;
        gap: 20px;

        .imageBox {
          width: 180px;
          height: 238px;
          cursor: pointer;
          overflow: hidden;
          position: relative;
          border-radius: 11px;
          background: linear-gradient(135deg, #fafafa 0%, #e6e6e6 100%);
          border: 2px solid transparent;

          // &:hover {
          // border: 1px solid #1777ff;
          // background: #FAFAFA;
          // box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.15);
          // }

          &.selected {
            border: 2px solid #1777ff;
          }

          .img {
            padding-left: 20px;
            width: 148px;
            height: 224px;
            object-fit: contain;
          }

          .roleName {
            position: absolute;
            bottom: -2px;
            width: 180px;
            height: 38px;
            background: rgba(226, 233, 242, 0.8);
            border-radius: 0px 0px 11px 11px;
            backdrop-filter: blur(5.43435347681893px);
            z-index: 10;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            // margin-left: 6px;
          }
        }
      }
    }

    .digital-person-title {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 18px;
      color: #17181a;
      line-height: 28px;
      text-align: left;
      font-style: normal;
      margin-bottom: 12px;
    }
  }

  .video-hover-wrapper {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
  }
  .video-hover-icon {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    pointer-events: auto;
    cursor: pointer;
  }
  .more-icon {
    position: absolute;
    right: 0;
    top: 0;
    border: none;
    z-index: 2;
    background: none;
    pointer-events: auto;
    cursor: pointer;
    border: none !important;
    box-shadow: none !important;
  }
  .more-icon .hover-show-icon {
    padding: 3px 0;
    display: none;
  }
  .more-icon:hover .hover-show-icon {
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
    background: #f2f8ff;
    display: block;
    border-radius: 2px;
    // opacity: 0.8;
  }

  :deep(.ant-menu-item:hover),
  :deep(.ant-menu-item-active),
  :deep(.ant-dropdown-menu-item:hover),
  :deep(.ant-dropdown-menu-item-active) {
    background: #f2f8ff !important;
  }

  :deep(.ant-popconfirm-message-icon) {
    display: none;
  }
</style>
