<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { reactive, onBeforeUnmount } from 'vue';
  import { getSpeakersList, getVoiceList, deleteVoice, renameVoice, StartCloning } from '@/api/avatarChat';
  import Icon from '@/components/Icon/index.vue';
  import AddModal from './AddModal/index.vue';
  import { getLocalItem } from '@/utils/common';
  import Loading from '../Loading/index.vue';
  import { Modal } from 'ant-design-vue';

  import videoIcon from '@/assets/image/base/pictures/video.png';
  import stopIcon from '@/assets/image/base/pictures/stop.png';
  import playIcon from '@/assets/image/base/pictures/play.png';
  import EmptyImage from '@/assets/image/base/pictures/empty_project.png';

  const speakersData = ref<Record<string, any[]>>({}); // 说话人音色列表
  const tagsData = reactive(['普通话', '方言', '外语']);
  // 映射标签到数据键
  const tagToKeyMap = {
    普通话: 'mandarin',
    方言: 'dialect',
    外语: 'foreign',
  };

  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');

  // 当前选中的语言类型
  const selectedLanguage = ref('mandarin');
  const selectedTag = ref<string | null>('普通话');
  const isPlaying = ref<number | null>(null); // 当前播放的index（全局唯一）
  const showAddModal = ref<boolean>(false);
  const voiceList = ref<any[]>([]);
  // const isOpenModal = ref(false);

  // 我的声音
  const myVoiceHoverIndex = ref<number | null>(null);
  const selectedVoiceIndex = ref<number | null>(null);
  const voiceListLoading = ref<boolean>(false);

  // 公共声音
  const publicHoverIndex = ref<number | null>(null);
  const selectedSpeaker = ref<string | null>(null);

  const nameInputError = ref(false);

  // 轮询定时器，用于检查克隆状态
  let pollingTimer: any = null;
  let pollingCount = 0; // 轮询次数计数器
  const MAX_POLL_DURATION_MS = 15 * 60 * 1000; // 最长轮询时间15分钟
  let pollingStartAt: number | null = null;

  let audio: HTMLAudioElement | null = null;

  // 暴露给父组件的暂停方法
  defineExpose({
    pauseAudio: () => {
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }
      isPlaying.value = null;
    },
  });

  const getVoiceListData = async () => {
    voiceListLoading.value = true;
    try {
      const data = await getVoiceList(userId);
      voiceList.value = data;
      // 仅当存在 processing 时启动轮询，否则停止轮询
      const hasProcessing = data.some((item: any) => item.status === 'processing');
      if (hasProcessing) {
        startPolling();
      } else {
        stopPolling();
      }
    } catch (error) {
      console.error('获取声音列表失败:', error);
    } finally {
      voiceListLoading.value = false;
    }
  };

  // 手动启动轮询的方法，用于外部调用
  const startPollingManually = () => {
    startPolling();
  };

  // 开始轮询检查克隆状态 - 使用渐进式轮询策略（持续轮询）
  const startPolling = () => {
    if (pollingTimer) return; // 避免重复启动

    pollingCount = 0; // 重置计数器
    pollingStartAt = Date.now();

    const poll = async () => {
      // 超过最长轮询时间则停止
      if (pollingStartAt && Date.now() - pollingStartAt >= MAX_POLL_DURATION_MS) {
        stopPolling();
        return;
      }
      try {
        const data = await getVoiceList(userId);
        voiceList.value = data;
        const hasProcessing = data.some((item: any) => item.status === 'processing');
        if (!hasProcessing) {
          stopPolling();
          return;
        }

        // 处理中：渐进式高频轮询
        pollingCount++;
        let interval = 3000; // 兜底3秒
        if (pollingCount <= 10) {
          interval = 500; // 前10次
        } else if (pollingCount <= 50) {
          interval = 1000; // 10-50次
        }

        pollingTimer = setTimeout(poll, interval);
      } catch (error) {
        console.error('轮询获取声音列表失败:', error);
        // 出错时也要继续轮询，但间隔稍长一些
        pollingTimer = setTimeout(poll, 2000);
      }
    };

    // 立即执行第一次检查
    poll();
  };

  // 停止轮询
  const stopPolling = () => {
    if (pollingTimer) {
      clearTimeout(pollingTimer);
      pollingTimer = null;
    }
    pollingCount = 0; // 重置计数器
    pollingStartAt = null;
  };

  onMounted(() => {
    getVoiceListData();
    startPolling();
    // 页面可见、窗口聚焦、网络恢复时，立即刷新一次，保持状态最新
    const handleImmediateRefresh = () => {
      getVoiceListData();
    };
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') handleImmediateRefresh();
    });
    window.addEventListener('focus', handleImmediateRefresh);
    window.addEventListener('online', handleImmediateRefresh);

    // 在卸载时移除监听（在 onBeforeUnmount 中统一清理）
  });

  // 说话人音色数据
  const getSpeakersData = async () => {
    try {
      const data = await getSpeakersList();
      speakersData.value = data; // 设置音色数据
    } catch (error) {
      console.error('获取音色列表失败:', error);
    }
  };

  // 页面加载时调用 getBackgroundData
  onMounted(() => {
    getSpeakersData();
  });

  // 管理音频元素
  const audioElements = reactive<Record<number, HTMLAudioElement | null>>({});

  const handleChange = (tag: string) => {
    selectedTag.value = tag; // 设置当前选中的标签
    // 获取对应的数据键
    const key = tagToKeyMap[tag as keyof typeof tagToKeyMap];
    if (key) {
      selectedLanguage.value = key;

      // 停止当前播放的音频
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
        audio = null;
      }

      // 清空播放状态
      isPlaying.value = null;

      // 清空所有音频元素
      Object.values(audioElements).forEach((audioElement) => {
        audioElement?.pause();
        audioElement?.remove();
      });
      Object.keys(audioElements).forEach((index) => {
        audioElements[Number(index)] = null;
      });
    } else {
      console.warn(`未找到与标签 "${tag}" 对应的语言类型`);
    }
  };

  // 播放音频
  const playAudio = (index: number, url: string) => {
    // 如果当前已在播放，暂停
    if (isPlaying.value === index) {
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
        audio = null;
      }
      isPlaying.value = null;
      return;
    }

    // 每次播放前都停止上一个音频
    if (audio) {
      audio.pause();
      audio.currentTime = 0;
      audio = null;
    }

    // 创建新的音频实例
    audio = new Audio(url);
    isPlaying.value = index;

    audio.play().catch((error) => {
      console.error('音频播放失败:', error);
      isPlaying.value = null;
    });

    audio.onended = () => {
      isPlaying.value = null;
      audio = null;
    };

    audio.onerror = () => {
      console.error('音频加载失败');
      isPlaying.value = null;
      audio = null;
    };
  };

  // 组件卸载时销毁音频元素和清理定时器
  onBeforeUnmount(() => {
    // 停止并清理当前音频
    if (audio) {
      audio.pause();
      audio.currentTime = 0;
      audio = null;
    }

    // 清理所有音频元素
    Object.values(audioElements).forEach((audioElement) => {
      if (audioElement) {
        audioElement.pause();
        audioElement.currentTime = 0;
      }
    });

    // 清空播放状态
    isPlaying.value = null;

    // 清理轮询定时器
    stopPolling();
    // 清理事件监听
    window.removeEventListener('focus', getVoiceListData as any);
    window.removeEventListener('online', getVoiceListData as any);
    // visibilitychange 回调是匿名的，这里无需额外处理
  });

  // 初始化默认选择
  // initializeDefaultSelection();

  const handleSelectMyVoice = (index: number) => {
    // 停止当前播放的音频
    if (audio) {
      audio.pause();
      audio.currentTime = 0;
      audio = null;
    }
    isPlaying.value = null;

    selectedVoiceIndex.value = index;
    selectedSpeaker.value = null; // 取消公共声音的选中
  };

  const handleSelectPublicSpeaker = (name: string) => {
    // 停止当前播放的音频
    if (audio) {
      audio.pause();
      audio.currentTime = 0;
      audio = null;
    }
    isPlaying.value = null;

    selectedSpeaker.value = name;
    selectedVoiceIndex.value = null; // 取消我的声音的选中
  };

  // 删除声音，增加二次确认
  const handleDelete = (item: any) => {
    Modal.confirm({
      title: `确定要删除“${item.name}”吗？`,
      content: '删除后不可恢复',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteVoice(item.id);
          // 删除成功后刷新列表
          getVoiceListData();
        } catch (error) {
          // 可以在这里添加错误提示
          console.error('删除失败', error);
        }
      },
    });
  };

  // 重命名相关变量
  const openNameModal = ref(false);
  const personName = ref('');
  const currentRenamingItem = ref<any>(null);

  // 打开重命名弹窗
  const handleOpenRenameModal = (item: any) => {
    personName.value = item.description || item.name;
    openNameModal.value = true;
    currentRenamingItem.value = item;
  };

  // 确认重命名
  const handleReName = async (item: any) => {
    if (!personName.value) {
      nameInputError.value = true;
      return;
    }
    if (!currentRenamingItem.value) {
      return;
    }
    try {
      await renameVoice({ voice_id: item.id, new_name: personName.value, user_id: userId });
      openNameModal.value = false;
      personName.value = '';
      currentRenamingItem.value = null;
      getVoiceListData(); // 刷新列表
    } catch (error) {
      // 可以在这里添加错误提示
      console.error('重命名失败', error);
    }
  };

  const handleReGenerate = async (item: any) => {
    console.log('重新生成', item);
    try {
      // 先停止现有轮询
      stopPolling();

      // 启动克隆
      StartCloning(item.id);

      // 立即刷新一次数据
      getVoiceListData();

      // 立即开始高频轮询
      startPolling();
    } catch (error) {
      console.error('重新生成失败', error);
    }
  };

  const handleFailDelete = async (item: any) => {
    console.log('删除失败', item);
    try {
      await deleteVoice(item.id);
      getVoiceListData();
    } catch (error) {
      console.error('删除失败', error);
    }
  };

  const handleRefreshMyVoiceData = async () => {
    // 立即获取最新数据
    // debugger;
    console.log('handleRefreshMyVoiceData6666666666666');
    await getVoiceListData();

    // 检查是否有正在处理中的声音，如果有则立即开始轮询
    const hasProcessing = voiceList.value.some((item: any) => item.status === 'processing');
    if (hasProcessing) {
      // 如果当前没有轮询，则立即开始
      if (!pollingTimer) {
        startPolling();
      }
    }
  };
</script>

<template>
  <div class="speakers-container">
    <div class="my-speakers-container">
      <span class="speakers-container-title">我的声音</span>
      <a-button type="primary" class="create-speaker-button" @click="showAddModal = true">
        <Icon name="jia" :size="16" />
        <span class="create-speaker-button-text">创建声音</span>
      </a-button>

      <div class="image-box">
        <div v-if="voiceList.length === 0" class="empty-image-box">
          <img :src="EmptyImage" alt="" class="empty-image" />
          <span class="text">你还没有创建声音哦，快去创建吧</span>
        </div>

        <div v-else class="speakers-container-image">
          <div
            v-for="(item, index) in voiceList"
            :key="index"
            class="imageBox"
            :class="{ selected: selectedVoiceIndex === index }"
            @click="handleSelectMyVoice(index)"
            @mouseenter="myVoiceHoverIndex = index"
            @mouseleave="myVoiceHoverIndex = null"
          >
            <!-- 失败 -->
            <template v-if="item.status === 'failed'">
              <img :src="videoIcon" class="img" />
              <Loading
                state="声⾳⽣成失败，请重新⽣成"
                :name="item.name"
                :handle-re-generate="() => handleReGenerate(item)"
                :handle-delete="() => handleFailDelete(item)"
              />
            </template>

            <!-- 处理中 -->

            <template v-else-if="item.status === 'processing'">
              <img :src="videoIcon" class="img" />
              <Loading state="克隆中" />
            </template>

            <!-- 成功 -->
            <template v-else>
              <div class="img-container" :class="{ grayscale: myVoiceHoverIndex === index || isPlaying === index }">
                <img :src="videoIcon" class="img" />
                <div
                  v-if="myVoiceHoverIndex === index || isPlaying === index"
                  class="play-area"
                  @click.stop="playAudio(index, item.sample_minio_url)"
                >
                  <img v-if="isPlaying !== index" :src="playIcon" alt="播放" />
                  <img v-else :src="stopIcon" alt="停止" />
                </div>
                <!-- hover 显示更多（三点）菜单 -->
                <a-dropdown v-show="myVoiceHoverIndex === index" placement="bottomRight" trigger="hover">
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="handleOpenRenameModal(item)">重命名</a-menu-item>
                      <a-menu-item @click="handleDelete(item)">删除</a-menu-item>
                    </a-menu>
                  </template>
                  <Icon
                    v-show="myVoiceHoverIndex === index"
                    name="gengduo"
                    :size="20"
                    class="more-icon"
                    style="position: absolute; right: -25px; top: -15px"
                  />
                </a-dropdown>
                <!-- 重命名弹窗 -->
                <a-popover
                  :open="openNameModal && currentRenamingItem === item"
                  trigger="click"
                  placement="topRight"
                  @update:open="
                    (visible: boolean) => {
                      if (!visible) {
                        openNameModal = false;
                        personName = '';
                        currentRenamingItem = null;
                      }
                    }
                  "
                >
                  <template #content>
                    <div style="width: 220px">
                      <div style="margin-bottom: 8px">重命名</div>
                      <a-input
                        v-model:value="personName"
                        maxlength="10"
                        clear
                        placeholder="请输入新名称，10个字内"
                        style="margin-bottom: 8px"
                        :status="nameInputError ? 'error' : ''"
                        @input="
                          () => {
                            if (personName) nameInputError = false;
                          }
                        "
                      />
                      <div style="text-align: right">
                        <a-button
                          size="small"
                          style="margin-right: 8px"
                          @click="
                            () => {
                              openNameModal = false;
                              personName = '';
                              currentRenamingItem = null;
                            }
                          "
                          >取消</a-button
                        >
                        <a-button size="small" type="primary" @click="handleReName(item)">确定</a-button>
                      </div>
                    </div>
                  </template>
                  <div></div>
                </a-popover>
              </div>
              <div class="name">{{ item?.description || item?.name }}</div>
            </template>
          </div>
        </div>
      </div>
    </div>

    <div class="public-speakers-container">
      <span class="speakers-container-title">公共声音</span>
      <div class="speakers-tag">
        <a-tag
          v-for="tag in tagsData"
          :key="tag"
          :class="{ selected: selectedTag === tag }"
          @click="() => handleChange(tag)"
        >
          {{ tag }}
        </a-tag>
      </div>
      <div class="speakers-container-image">
        <div
          v-for="(item, index) in speakersData[selectedLanguage]"
          :key="index"
          class="imageBox"
          :class="{ selected: selectedSpeaker === item.name }"
          @click="handleSelectPublicSpeaker(item.name)"
          @mouseenter="publicHoverIndex = index"
          @mouseleave="publicHoverIndex = null"
        >
          <div class="img-container" :class="{ grayscale: publicHoverIndex === index || isPlaying === index + 10000 }">
            <img class="img" :src="item.image_url" :alt="`${item.name} ${index}`" />
            <!-- 播放区域 - 点击试听 -->
            <div
              v-if="publicHoverIndex === index || isPlaying === index + 10000"
              class="play-area"
              @click.stop="playAudio(index + 10000, item.sample_file_url)"
            >
              <img v-if="isPlaying !== index + 10000" :src="playIcon" alt="播放" />
              <img v-else :src="stopIcon" alt="停止" />
            </div>
          </div>
          <div class="name">{{ item?.description || item?.name }}</div>
        </div>
      </div>
    </div>

    <AddModal v-if="showAddModal" v-model:visible="showAddModal" @refresh-my-voice-data="handleRefreshMyVoiceData" />
    <!-- <AddModal v-if="showAddModal" v-model:visible="showAddModal" @refresh-speakers-data="getVoiceListData" /> -->
  </div>
</template>

<style lang="less" scoped>
  .speakers-container {
    width: 100%;
    height: calc(100vh - 230px);
    overflow: auto; /* 防止内容溢出 */
    background: #ffffff;

    .my-speakers-container {
      display: flex;
      flex-direction: column;

      .create-speaker-button {
        margin: 12px 0 12px 20px;
        width: 112px;
        height: 40px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #ffffff;
        line-height: 20px;
        text-align: left;

        .create-speaker-button-text {
          margin-left: 5px;
        }
      }

      .image-box {
        .empty-image-box {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          margin: 60px;

          .text {
            margin-top: 12px;
            width: 210px;
            height: 22px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #636466;
            line-height: 22px;
            text-align: right;
            font-style: normal;
          }
        }

        .speakers-container-image {
          display: flex;
          justify-content: flex-start;
          flex-wrap: wrap;
          max-height: 623px; /* 设置固定高度 */
          overflow-y: auto; /* 启用纵向滚动 */
          overflow-x: hidden; /* 禁用横向滚动 */
          background: #ffffff;
          // box-shadow: 4px 2px 24px 0px rgba(123, 123, 123, 0.1);

          /* 隐藏滚动条 */
          ::-webkit-scrollbar {
            display: none; /* 隐藏滚动条 */
          }

          -ms-overflow-style: none; /* 适用于 IE 和 Edge */
          scrollbar-width: none; /* 适用于 Firefox */

          .imageBox {
            width: 134px;
            height: 128px;
            cursor: pointer;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            margin: 10px;
            background: #fafafa;
            /* box-shadow: 4px 2px 24px 0px rgba(123, 123, 123, 0.1); */

            .img {
              width: 60px;
              height: 60px;
              /* margin: 2px 0px; */
              /* border-radius: 8px; */
            }

            &:hover {
              // border: 1px solid #1777ff;
              background: #fafafa;
              box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
              border-radius: 10px;
            }

            &.selected {
              border: 2px solid #1777ff;
            }

            .img-container {
              position: relative;
            }

            .grayscale {
              filter: grayscale(100%);
            }

            .play-area {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              display: flex;
              justify-content: center;
              align-items: center;
              width: 60px;
              height: 60px;
              background: rgba(23, 24, 26, 0.3);
              border-radius: 50%;
              cursor: pointer;
              transition: all 0.3s ease;

              // &:hover {
              //   // opacity: 0.5;
              //   background: rgba(23,24,26,0.5);
              // }

              img {
                width: 30px;
                height: 30px;
              }
            }

            .name {
              margin-top: 4px;
            }
          }
        }
      }
    }

    .public-speakers-container {
      padding-top: 30px;
      .speakers-tag {
        margin-bottom: 10px;
        display: flex;
        justify-content: flex-start;
        height: 32px;
        margin: 10px 20px;
        // background: #ffffff !important;

        :deep(.ant-tag) {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          padding: 7px 14px;
          background: #ffffff;

          font-size: 14px;
          color: #969799;
          line-height: 20px;
          text-align: right;
          border: none;
          &:hover {
            color: #000000;
          }
          &.selected {
            background: #f2f8ff;
            color: #000000;
          }

          // 禁用点击时的扩散效果
          &:active {
            transform: none !important;
            transition: none !important;
          }

          // 禁用所有可能的动画效果
          * {
            transition: none !important;
            animation: none !important;
          }
        }
      }

      .speakers-container-image {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        max-height: 623px; /* 设置固定高度 */
        overflow-y: auto; /* 启用纵向滚动 */
        overflow-x: hidden; /* 禁用横向滚动 */
        background: #ffffff;
        // box-shadow: 4px 2px 24px 0px rgba(123, 123, 123, 0.1);

        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
          display: none; /* 隐藏滚动条 */
        }

        -ms-overflow-style: none; /* 适用于 IE 和 Edge */
        scrollbar-width: none; /* 适用于 Firefox */

        .imageBox {
          width: 134px;
          height: 128px;
          cursor: pointer;
          overflow: hidden;
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border-radius: 10px;
          margin: 10px;
          background: #fafafa;
          /* box-shadow: 4px 2px 24px 0px rgba(123, 123, 123, 0.1); */

          .img {
            width: 60px;
            height: 60px;
            /* margin: 2px 0px; */
            /* border-radius: 8px; */
          }

          &:hover {
            // border: 1px solid #1777ff;
            background: #fafafa;
            box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.15);
            border-radius: 10px;
          }

          &.selected {
            border: 2px solid #1777ff;
          }

          .img-container {
            position: relative;
          }

          .grayscale {
            filter: grayscale(100%);
          }

          .play-area {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            align-items: center;
            width: 60px;
            height: 60px;
            background: rgba(23, 24, 26, 0.3);
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;

            // &:hover {
            //   // opacity: 0.5;
            //   background: rgba(23,24,26,0.5);
            // }

            img {
              width: 30px;
              height: 30px;
            }
          }

          .name {
            margin-top: 4px;
          }
        }
      }
    }

    .speakers-container-title {
      width: 72px;
      height: 28px;
      margin-left: 20px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 18px;
      color: #17181a;
      line-height: 28px;
      text-align: left;
      font-style: normal;
    }
  }
  // :global(.ant-modal .ant-modal-body) {
  //   padding: 0 30px 20px;
  // }
</style>
