<script setup lang="ts">
  import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
  import { message } from 'ant-design-vue';
  import Background from './components/Backgrund.vue';
  import Roles from './components/RolesChange.vue';
  import Speakers from './components/SpeakersChange.vue';
  import { UpOutlined, UserOutlined, InfoCircleOutlined } from '@ant-design/icons-vue';
  import Icon from '@/components/Icon/index.vue';
  import type { ChatWsProps, Message } from '@/interface/avatarChat';
  import { getBackgroundImage, getSpeakersList, getVoiceList } from '@/api/avatarChat';
  import toggleIcon from '@/assets/image/base/pictures/toggle.png';
  import sendIconHover from '@/assets/image/base/pictures/send_hover.png';
  import sendIconGrey from '@/assets/image/base/pictures/send.png';
  import sendBtn from '@/assets/image/base/pictures/sendbtn.png';
  import toggleUnfold from '@/assets/image/base/pictures/toggle_unfold.png';
  import failModelIcon from '@/assets/image/base/pictures/failModel.png';
  import voice from '@/assets/image/base/pictures/voice.png';
  import Recording from '@/assets/image/base/pictures/voice.gif';
  import noModelIcon from '@/assets/image/base/pictures/noModel.png';

  import { useThrottle } from '@/hooks/useThrottle';
  import { WebSocketClient as webSocketClass } from '@/utils/ws';
  import config from '@/config';
  import { getLocalItem } from '@/utils/common';
  import FlvLivePlayer from './components/flvVideo.vue';
  import Detail from './Detail/index.vue';
  import { recOpen, recStart, recStop } from '@/utils/recorder';
  import { getModelList } from '@/api/textToImage';

  interface asrWsProps {
    answer: string;
    isEnd?: boolean;
    isLast?: boolean;
    isFinal?: boolean;
  }

  const activeTab = ref('1');
  const inputQuestionValue = ref(''); // 输入框的值
  const bubbleText = ref<string | null>(null); // 气泡文本
  const showRecommend = ref(true); // 控制是否显示推荐问题
  const showDetailsButton = ref(false); // 控制按钮是否显示
  const initanswers = ['你是谁？', '介绍一下深圳', '介绍一下环球数科股份有限公司'];
  const isModalOpen = ref(false); // 是否打开详情弹窗
  const isCollapsed = ref(false); // 是否打开菜单功能栏
  const hoveredButton = ref(false); // 控制按钮的 hover 状态
  const bubbleTimeoutRef = ref<number | null>(null); // 定义气泡超时引用
  const selectedSpeaker = ref('xingxing'); // 默认选中的说话人
  const selectedRole = ref<string | undefined>(undefined); // 选中的角色
  const selectedBackground = ref('https://minio-test.shukeyun.com/avatar/virtual_classroom/bg/bg1.png'); // 当前选中的背景
  const selectedSpeakerId = ref<string | undefined>(undefined);
  const selectedSpeakerType = ref<string | undefined>('mandarin');

  const isInit = ref<string>('4'); // 模型是否是初始化（'1'：初始化，'2'：加载成功，'3'：失败，需要重新加载， '4': 未加载模型）
  const liveURL = ref<string>(''); // 定义直播视频链接
  const muted = ref<boolean>(true); // 定义是否静音
  const contextId = ref<string>(''); // 定义上下文ID
  const state = reactive({
    messageList: [] as Message[], // 消息列表
  });

  const backgroundData = ref(['https://minio-test.shukeyun.com/avatar/virtual_classroom/bg/bg1.png']);
  const speakersData = ref({}); // 说话人音色列表
  let hasErrorMessage = false;

  const isRecording = ref(false); // 是否正在录音
  const recordingText = ref(''); // 录音文本

  const modelList = ref<any>([]);
  const activeModel = ref<any>(null);
  const selectedRoleIndex = ref(0);
  const selectedRoleSource = ref('library');

  const voiceList = ref([]);
  const voiceListLoading = ref(false);
  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');

  const getParams = (param: any) => {
    // 判断当前是否为“我的声音”
    const isMyVoice = selectedSpeakerType.value === 'my_voice';
    return {
      user_id: userId,
      source: 'h5',
      avatar_model: 'avatar01_2d',
      background: selectedBackground.value,
      speaker: isMyVoice ? selectedSpeakerId.value : selectedSpeaker.value,
      context_id: contextId.value,
      diffusion_avatar_id: selectedRole.value || undefined,
      ...param,
    };
  };

  const handleSpeakerSelected = (name: string) => {
    // console.log(name, 'name1111')
    selectedSpeaker.value = name;
  };
  const handleSpeakerSelectedId = (id: string) => {
    // console.log(id, 'name11112222')
    selectedSpeakerId.value = id;
  };

  const handleSpeakerSelectedType = (type: string) => {
    // console.log(type, 'name11113333')
    selectedSpeakerType.value = type;
  };

  // 处理角色选择
  const handleRoleSelected = (payload) => {
    if (payload.source === 'library') {
      selectedRole.value = undefined;
      selectedRoleIndex.value = payload.index;
      selectedRoleSource.value = 'library';
      selectedSpeaker.value = 'xingxing';
    } else if (payload.source === 'my') {
      selectedRole.value = payload.role.avatar_id;
      selectedRoleIndex.value = payload.index;
      selectedRoleSource.value = 'my';
      selectedSpeaker.value = payload.role.default_speaker || 'xingxing';
    }
  };

  // 处理背景选择
  const handleBackgroundSelected = (backgroundUrl: string) => {
    selectedBackground.value = backgroundUrl;
  };

  // 创建 WebSocket 实例
  const chatWs = new webSocketClass(config.avatorWs, (data: ChatWsProps) => {
    getMsgAndVideoList(data);
  });

  // 处理 beforeunload 事件
  const handleBeforeUnload = () => {
    if (chatWs && typeof chatWs.close === 'function') {
      chatWs.close();
    }
  };

  // 页面可见性变化处理
  const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible') {
      // 页面变为可见时，检查连接状态
      if (chatWs && typeof chatWs.connect === 'function') {
        chatWs.connect();
      }
    } else {
      // 页面变为隐藏时，可以选择保持连接或关闭
      // 这里选择保持连接，因为用户可能很快回来
    }
  };

  // 页面加载时连接 WebSocket
  onMounted(() => {
    // 初始化连接
    if (chatWs && typeof chatWs.connect === 'function') {
      chatWs.connect();
    }

    // 添加页面卸载事件监听器
    window.addEventListener('beforeunload', handleBeforeUnload);

    // 添加页面可见性变化监听器
    document.addEventListener('visibilitychange', handleVisibilityChange);
  });

  // 页面卸载时关闭 WebSocket
  onBeforeUnmount(() => {
    // 关闭WebSocket连接
    handleBeforeUnload();

    // 关闭语音识别WebSocket连接
    if (asrWs && typeof asrWs.close === 'function') {
      asrWs.close();
    }

    // 移除事件监听器
    window.removeEventListener('beforeunload', handleBeforeUnload);
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  });

  // 页面加载时执行模型加载
  // onMounted(() => {
  //   setTimeout(() => {
  //     chatWs?.send(getParams({ type: 'avatar_init' }));
  //   }, 2000);
  // });

  const showModal = () => {
    isModalOpen.value = true;
    showDetailsButton.value = false;
    showRecommend.value = false;
  };

  // 关闭对话详情
  const handleCloseDetail = () => {
    isModalOpen.value = false;
    showDetailsButton.value = true;
    showRecommend.value = false;
  };

  // 方法：鼠标进入按钮
  const handleMouseEnter = () => {
    hoveredButton.value = true;
  };

  // 方法：鼠标离开按钮
  const handleMouseLeave = () => {
    hoveredButton.value = false;
  };

  const setInputValue = (value: string) => {
    inputQuestionValue.value = value;
  };

  // 向上滚动
  const autoScroll = () => {
    setTimeout(() => {
      // 一句话占据两行时，要等两行填满高度后再进行滚动至最高
      const element = document.getElementById('msg-box-id');
      if (element) {
        element.scrollTop = element.scrollHeight;
      }
    }, 0);
  };

  /* 获取视频 & 文字列表 */
  const getMsgAndVideoList = (res: ChatWsProps) => {
    const { answer = '', live_url, code, context_id } = res;
    const isError = [500, 501, 502, 503, 505, 507, 510].includes(code);
    const isStart = [200, 201].includes(code);
    // 错误
    if (isError) {
      if (!hasErrorMessage) {
        state.messageList.push({
          type: 'answer',
          text: '没能明白您的意思，请重新提问~',
        });
        hasErrorMessage = true; // 设置标志位为 true
      }
      autoScroll();

      // chatWs.resetConnection();
      return;
    }

    // 如果不是错误状态码，重置标志位
    hasErrorMessage = false;

    // 加载模型success
    if (code == 210) {
      const flvUrl = live_url?.hls_flv;
      if (flvUrl && flvUrl.trim() !== '') {
        liveURL.value = flvUrl;
        console.log('设置直播URL:', flvUrl);
      } else {
        console.warn('直播URL为空或无效:', flvUrl);
        liveURL.value = '';
      }
      isInit.value = '2';
      contextId.value = context_id || '';
    }

    // init加载模型出错
    if (code == 514) {
      isInit.value = '3';
    }

    // reInit加载模型出错
    if (code == 513) {
      isInit.value = '3';
    }

    // 文字开始传输
    if (code == 200) {
      muted.value = false;
    }

    // 文字list
    if (isStart) {
      // 将回答加入消息列表
      if (answer) {
        const lastMessage = state.messageList.slice(-1)[0];
        if (lastMessage && lastMessage.type === 'answer') {
          // 如果最后一条消息是回答，追加内容
          lastMessage.text += answer;
        } else {
          // 否则新增一条回答
          state.messageList.push({
            type: 'answer',
            text: answer,
          });
        }
      }
      autoScroll();
    }
  };

  // 发送消息
  const sendMsg = useThrottle((text?: string) => {
    const question = text || inputQuestionValue;
    let questionStr = typeof question === 'object' && 'value' in question ? question.value : question;
    if (!questionStr || !questionStr.trim()) {
      message.warning('请输入问题');
      return;
    }
    // resetTimeout(sessionTimeoutRef);
    // 截断超过 50 个字符的内容
    const truncatedText =
      typeof question === 'object' && 'value' in question
        ? question.value.length > 50
          ? `${question.value.slice(0, 50)}...`
          : question.value
        : question.length > 50
          ? `${question.slice(0, 50)}...`
          : question;
    if (!bubbleText.value) {
      bubbleText.value = truncatedText;
    }
    if (!question) {
      message.warning('请输入问题');
      return;
    }

    inputQuestionValue.value = '';

    // 将提问加入消息列表
    state.messageList.push({
      type: 'question',
      text: typeof question === 'string' ? question : question.value,
    });

    chatWs?.send(getParams({ question: question }));
    showRecommend.value = false;
    if (!isModalOpen.value) {
      showDetailsButton.value = true;
    }

    if (bubbleTimeoutRef.value && bubbleTimeoutRef.value) {
      clearTimeout(bubbleTimeoutRef.value);
    }

    bubbleTimeoutRef.value = setTimeout(() => {
      bubbleText.value = null;
    }, 10000);
  }, 3000);

  // 背景数据
  const getBackgroundData = async () => {
    try {
      const data = await getBackgroundImage(); // 调用接口
      if (Array.isArray(data) && data.length > 0) {
        backgroundData.value = data; // 设置背景数据
      }
    } catch (error) {
      console.error('获取背景图列表失败:', error);
    }
  };

  // 说话人音色数据
  const getSpeakersData = async () => {
    try {
      const data = await getSpeakersList();
      speakersData.value = data;
      // speakersData.value.my_voice = voiceList.value; // 设置音色数据
    } catch (error) {
      console.error('获取音色列表失败:', error);
    }
  };

  console.log(speakersData.value, 'speakersData.value');

  const getVoiceListData = async () => {
    voiceListLoading.value = true;
    try {
      const data = await getVoiceList(userId);
      voiceList.value = data;
    } catch (error) {
      console.error('获取声音列表失败:', error);
    } finally {
      voiceListLoading.value = false;
    }
  };

  onMounted(() => {
    getVoiceListData();
  });

  // 页面加载时调用 getBackgroundData
  onMounted(async () => {
    getBackgroundData();
    getSpeakersData();
    const models = await getModelList();
    modelList.value = models?.['audio-to-video'];
    activeModel.value = modelList.value[0];
  });

  const getAudioToTxt = (data: asrWsProps) => {
    let tempText = data?.answer?.replace(/no/, '');
    inputQuestionValue.value = tempText;
  };

  // 语音识别 WS
  const asrWs = new webSocketClass(config.asr, (data: asrWsProps) => {
    if (data?.isFinal) {
      getAudioToTxt(data);
    }
  });
  const handleRecorder = () => {
    asrWs.connect();

    recOpen(
      () => {
        recStart();
        isRecording.value = true;
        // asrWs?.send(res);
      },
      (res: string) => {
        console.error(res || '获取录音权限失败！');
        message.error('获取录音权限失败！');
      },

      (res: any) => {
        recordingText.value = res;
        asrWs?.send(res);
      },
    );
  };

  const stopRecording = () => {
    recStop((res: any) => {
      asrWs?.send(res);
    });
    isRecording.value = false;
  };

  const cancelRecording = () => {
    isRecording.value = false;
  };

  // onMounted(() => {
  //   asrWs?.connect();
  // });

  const reStart = () => {
    chatWs.close();
    setTimeout(() => {
      chatWs?.send(getParams({ type: 'avatar_init' }));
    }, 10000);
    isInit.value = '1';
  };

  const handleTabChange = (key: string) => {
    activeTab.value = key;
  };

  const openChat = () => {
    chatWs?.send(getParams({ type: 'avatar_init' }));
    isInit.value = '1';
  };

  // const resetModel = () => {
  //   chatWs?.close();
  //   isInit.value = '4';
  // };

  // const flvLivePlayerStyle = computed(() => {
  //   if (isCollapsed.value) {
  //     // 菜单收起，整体左移 162px
  //     return {
  //       left: 'calc(50% - 262px)',
  //       transform: 'translate(-50%, -50%)',
  //     };
  //   } else {
  //     // 菜单展开，正常居中
  //     return {
  //       left: '50%',
  //       transform: 'translate(-50%, -50%)',
  //     };
  //   }
  // });

  const refreshSpeakersData = async () => {
    await getSpeakersData();
    await getVoiceListData();
  };

  // 只刷新我的声音列表数据
  const refreshMyVoiceData = async () => {
    await getVoiceListData();
  };
</script>
<template>
  <div class="digitHuman_container">
    <FlvLivePlayer
      v-if="liveURL && liveURL.trim() !== '' && isInit === '2'"
      class="background-video"
      :src="liveURL"
      :muted="muted"
      :debug="true"
    />
    <!-- </template> -->

    <div class="digitHuman_content_box" :style="{ background: isInit !== '2' ? '#f5f5f5' : 'none' }">
      <div :style="{ width: !isCollapsed ? '324px' : '0px' }" class="left_menu_box">
        <div v-show="!isCollapsed" class="menu_content">
          <div class="tab-buttons">
            <div class="tab-button" :class="{ active: activeTab === '1' }" @click="() => handleTabChange('1')">
              <UserOutlined />
              数字人
            </div>
            <div class="tab-button" :class="{ active: activeTab === '2' }" @click="() => handleTabChange('2')">
              <template v-if="activeTab === '2'">
                <Icon name="a-beijing1" :size="16" />
              </template>
              <template v-else>
                <Icon name="beijing" :size="16" />
              </template>
              背景
            </div>
            <div class="tab-button" :class="{ active: activeTab === '3' }" @click="() => handleTabChange('3')">
              <template v-if="activeTab === '3'">
                <Icon name="a-yinse2x" :size="16" />
              </template>
              <template v-else>
                <Icon name="xingzhuang" :size="16" />
              </template>

              声音
            </div>
          </div>
          <div>
            <template v-if="activeTab === '1'">
              <Roles
                :is-collapsed="isCollapsed"
                :selected-role="selectedRole"
                :selected-role-index="selectedRoleIndex"
                :selected-role-source="selectedRoleSource"
                @role-selected="handleRoleSelected"
              />
            </template>
            <template v-if="activeTab === '2'">
              <Background
                :is-collapsed="isCollapsed"
                :background-data="selectedRoleSource === 'my' ? [backgroundData[0]] : backgroundData"
                :context-id="contextId"
                :is-init="isInit === '2' ? true : false"
                :selected-background="selectedBackground"
                :type="selectedRoleSource === 'my' ? 'private' : 'public'"
                :selected-role-source="selectedRoleSource"
                @role-selected="handleRoleSelected"
                @background-selected="handleBackgroundSelected"
              />
            </template>
            <template v-if="activeTab === '3'">
              <Speakers
                :is-collapsed="isCollapsed"
                :speakers-data="{ ...speakersData, my_voice: voiceList }"
                :context-id="contextId"
                :selected-speaker="selectedSpeaker"
                @speaker-selected="handleSpeakerSelected"
                @speaker-selected-id="handleSpeakerSelectedId"
                @speaker-selected-type="handleSpeakerSelectedType"
                @refresh-speakers-data="refreshSpeakersData"
                @refresh-my-voice-data="refreshMyVoiceData"
              />
            </template>
          </div>
        </div>

        <!-- <div class="menu_divider"></div>

        <div class="menu_button">
          <a-button @click="resetModel">重置</a-button>

          <a-button type="primary" @click="openChat">立即对话</a-button>
        </div> -->
      </div>

      <div class="toggle_icon">
        <a-image
          :src="isCollapsed ? toggleUnfold : toggleIcon"
          alt=""
          :preview="false"
          @click="isCollapsed = !isCollapsed"
        />
      </div>

      <div class="right_content_box">
        <template v-if="isInit === '2'">
          <div class="msg-box">
            <div class="btn_box">
              <div v-if="showDetailsButton" class="detailsButton" @click="showModal">
                对话详情
                <UpOutlined />
              </div>

              <!--气泡文本-->
              <div v-if="bubbleText && !isModalOpen" class="bubble_box">
                {{ bubbleText }}
              </div>
            </div>

            <!--推荐问题-->
            <div v-if="showRecommend" class="initQuestion">
              <div class="init_question_text">
                <div v-for="(item, index) in initanswers" :key="index" class="init_question_item">
                  <a-button
                    class="btn"
                    @mouseenter="handleMouseEnter"
                    @mouseleave="handleMouseLeave"
                    @click="
                      () => {
                        sendMsg(item);
                      }
                    "
                  >
                    {{ item }}
                    <a-image
                      class="icon"
                      :src="hoveredButton ? sendIconHover : sendIconGrey"
                      alt="发送图标"
                      :preview="false"
                    />
                  </a-button>
                </div>
              </div>
            </div>

            <!-- Detail 组件 -->
            <Detail
              v-if="isModalOpen"
              v-model:detail-data="state.messageList"
              :open-modal="isModalOpen"
              @close="handleCloseDetail"
            />

            <!-- 聊天输入框 -->
            <div v-if="!isRecording" class="chat-box">
              <a-textarea
                v-model:value="inputQuestionValue"
                class="custom-textarea"
                placeholder="请输入你的问题"
                :auto-size="{ minRows: 1, maxRows: 4 }"
                @change="
                  (e: any) => {
                    setInputValue(e.target.value);
                  }
                "
                @press-enter="
                  (e: any) => {
                    e.preventDefault();
                    sendMsg(inputQuestionValue);
                  }
                "
              />

              <a-image class="video-icon" :src="voice" alt="" :preview="false" @click="handleRecorder" />
              <div class="divider"></div>
              <a-image class="send-icon" :src="sendBtn" alt="" :preview="false" @click="sendMsg(inputQuestionValue)" />
            </div>

            <div v-if="isRecording" class="voice-box">
              <div class="custom-textarea">
                <a-button class="exit-recording" @click="cancelRecording">取消</a-button>

                <div class="recording-status">
                  <img class="waveform" :src="Recording" alt="录音波形" />
                  <span class="recording-text">录音中</span>
                </div>
                <a-button type="primary" class="exit-recording1" @click="stopRecording">转文字</a-button>

                <!-- <a-image
                  class="send-icon"
                  :src="sendBtn"
                  alt=""
                  :preview="false"
                  @click="sendMsg(inputQuestionValue)"
                /> -->
              </div>
            </div>
          </div>
        </template>
        <template v-if="isInit === '4'">
          <div class="noModel_box">
            <a-image :src="noModelIcon" alt="" :preview="false" />
            <p style="margin-bottom: 4px">左侧已有默认选项，可进行更改，或者直接点击立即对话吧</p>
            <a-button type="primary" style="margin-top: 16px; width: 140px" @click="openChat">立即对话</a-button>
          </div>
        </template>
        <template v-if="isInit === '1'">
          <div class="loader_box">
            <div class="loader"></div>
            <p>数字人加载中...</p>
          </div>
        </template>
        <template v-if="isInit === '3'">
          <div class="reLoader_box">
            <a-image :src="failModelIcon" alt="" :preview="false" />
            <p>数字人加载失败，请重试</p>
            <a-button type="primary" class="reLoadingBtn" ghost @click="reStart"> 重新加载 </a-button>
          </div>
        </template>
        <div class="model-nmae-option"></div>
        <div class="model-nmae">
          <span>{{ activeModel?.name || 'Fictionarry/ER-NeRF' }}</span>
          <a-tooltip placement="bottom" color="#fff" :overlay-inner-style="{ width: '320px' }">
            <template v-if="activeModel" #title>
              <div class="model-info">
                <div>{{ activeModel.name }} 基本信息</div>
                <div>
                  <a-tag v-for="(t, i) in activeModel.tags" :key="i" color="cyan">{{ t }}</a-tag>
                </div>
                <div>{{ activeModel.description }}</div>
              </div>
            </template>
            <InfoCircleOutlined class="info-icon" style="color: rgba(0, 0, 0, 0.35); cursor: pointer" />
          </a-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
  .digitHuman_container {
    position: relative;
    display: flex;
    flex-direction: column; /* 子元素垂直排列 */
    width: 100%;
    height: calc(100vh - 230px);
    overflow: hidden; /* 防止内容溢出 */

    /* 背景视频 */
    .background-video {
      position: absolute;
      top: 50%;
      left: 50%;
      min-width: 100vw;
      min-height: 100vh;
      width: auto;
      height: 100%;
      transform: translate(-50%, -50%);
      object-fit: cover;
      z-index: 0;
      pointer-events: none;
      background: #f7f8fa;
    }

    .digitHuman_content_box {
      position: relative;
      display: flex;
      width: 100%;
      height: 100%;
      // background: #f5f5f5;
      // 直播流当全背景
      .videoPlayer {
        position: relative;
        width: 100%;
        height: 100%;
      }

      // 左侧的功能栏
      .left_menu_box {
        transition: all 500ms;
        // top: 0;
        // left: 0;
        display: flex;
        // position: relative;
        width: 324px;
        height: 100%;
        background: #fff;
        justify-content: space-between;
        flex-direction: column;
        .menu_content {
          // position: relative;
          width: calc(100% - 0px);
          height: 100%;
          background: #fff;
          transition: all 0.3s ease; /* 添加过渡效果 */

          .tab-buttons {
            display: flex;
            justify-content: space-between;
            width: calc(100% - 40px);
            height: 40px;
            margin: 10px 20px;
            border-radius: 8px;
            background: #f2f2f2;
          }

          .tab-button {
            display: flex;
            width: 90px;
            height: 36px;
            text-align: center;
            font-weight: 500;
            font-family:
              PingFangSC,
              PingFang SC;

            white-space: nowrap;

            // overflow: hidden;
            // display: -webkit-box;
            // -webkit-box-orient: vertical;
            // -webkit-line-clamp: 1;

            justify-content: center;
            align-items: center;
            cursor: pointer;
            background: #f0f0f0;
            color: #939599;
            margin: 2px;
            border-radius: 8px;
            > * {
              margin-right: 3px;
            }
            &:not(:last-child) {
              border-right: none;
            }
            &.active {
              background: #ffffff;
              color: #1677ff;
            }
          }
        }

        .menu_divider {
          height: 1px;
          background: #e0e0e0;
          width: 100%;
          margin-bottom: 10px;
        }

        .menu_button {
          display: flex;
          justify-content: space-around;
          width: 324px;
          height: 48px;
          padding: 0 16px;
        }

        :deep(.ant-tabs-tab) {
          padding: 12px 40px;
          margin-left: 0 !important;
        }
      }

      .toggle_icon {
        position: relative;
        top: 45%;
        // height: 74px;
        width: 24px;
      }

      // 右侧的内容栏
      .right_content_box {
        position: relative;
        box-sizing: border-box;
        display: flex;
        flex: 1;
        flex-direction: column;
        padding: 0 12%;

        .noModel_box {
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
          height: 100%;
          > p {
            height: 20px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            margin-top: 16px;
            font-size: 14px;
            color: #969799;
            line-height: 20px;
            text-align: left;
          }
        }

        .loader_box {
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
          height: 100%;
          .loader {
            // position: absolute; /* 绝对定位 */
            // top: 50%; /* 垂直居中 */
            // left: 50%; /* 水平居中 */
            width: 45px;
            height: 45px; /* 确保 loader 是正方形 */
            aspect-ratio: 1;
            background:
              var(--c) 0% 50%,
              var(--c) 50% 50%,
              var(--c) 100% 50%;
            background-size: 20% 100%;
            transform: translate(-50%, -50%); /* 通过 transform 实现真正的居中 */
            animation: l1 1s infinite linear;

            --c: no-repeat linear-gradient(#1777ff 0 0); /* 设置颜色为 #1777FF */
          }

          > p {
            height: 20px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #1777ff;
            line-height: 20px;
            text-align: left;
          }

          // > p {
          //   // position: absolute;
          //   // top: calc(40% + 16px); /* 设置在 loader 下方，调整间距 */
          //   // left: 50%;
          //   // width: '163px';
          //   // height: '154px';
          //   margin-top: 16px;
          //   font-family: PingFangSC, 'PingFang SC';
          //   font-size: 16px;
          //   font-weight: 500;
          //   color: #1777ff; /* 设置文字颜色 */
          //   text-align: center;
          //   transform: translateX(-50%);
          //   white-space: nowrap; /* 防止文字换行 */
          //   overflow: hidden; /* 隐藏溢出的文本 */
          //   text-overflow: ellipsis; /* 溢出文本用省略号表示 */
          // }

          @keyframes l1 {
            0% {
              background-size:
                20% 100%,
                20% 100%,
                20% 100%;
            }

            33% {
              background-size:
                20% 10%,
                20% 100%,
                20% 100%;
            }

            50% {
              background-size:
                20% 100%,
                20% 10%,
                20% 100%;
            }

            66% {
              background-size:
                20% 100%,
                20% 100%,
                20% 10%;
            }

            100% {
              background-size:
                20% 100%,
                20% 100%,
                20% 100%;
            }
          }
        }

        .reLoader_box {
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
          height: 100%;
          .reLoadingBtn {
            font-size: 14px;
            font-weight: 400;
            color: #1777ff;

            &:hover {
              background: #d9e9ff !important;
            }
          }
        }

        .initQuestion {
          margin-bottom: 16px;

          .init_question_text {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            width: 50%;

            .init_question_item {
              width: 100%; /* 子元素占满父容器宽度 */
              margin: 2px 0; /* 按钮之间的间距 */
            }

            .btn {
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 100%;
              height: 44px;
              padding: 0 10px;
              margin: 6px 6px 6px 0;
              font-family: PingFangSC, 'PingFang SC';
              font-size: 16px;
              font-style: normal;
              font-weight: 400;
              line-height: 24px;
              color: #e6e6e6;
              text-align: left;
              background: rgb(72 74 77 / 79%);
              border: none;
              border-radius: 23px;
              item-align: flex-start;

              .icon {
                width: 20px;
                height: 20px;
              }

              &:hover {
                color: #333;
                background: rgb(217 222 230 / 79%);
                border: 2px solid #fff;

                icon {
                  color: #000;
                  background: #000;
                }
              }
            }
          }
        }

        .msg-box {
          position: absolute;
          bottom: 24px;
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          width: calc(100% - 12% * 2);
          height: auto;

          .btn_box {
            display: flex;
            justify-content: space-between;

            .detailsButton {
              bottom: 0;
              width: 108px;
              height: 38px;
              padding: 10px;
              margin-top: 16px;
              margin-bottom: 16px;
              color: #17181a;
              background: rgb(220 224 230 / 45%);
              border: none;
              border-radius: 8px;

              p {
                width: 64px;
                height: 22px;
                font-family: PingFangSC, 'PingFang SC';
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
                color: #17181a;
                text-align: left;
              }
            }

            .bubble_box {
              z-index: 1000;
              display: flex;
              justify-content: flex-end;
              width: auto;
              max-width: 86%;
              height: 56px;
              padding: 16px;
              margin-bottom: 10px;
              font-family: PingFangSC, 'PingFang SC';
              font-size: 16px;
              font-weight: 400;
              line-height: 24px;
              color: #000;
              text-align: center;
              background: rgb(241 247 255 / 80%);
              border-radius: 12px 12px 0;
              backdrop-filter: blur(2px);
            }
          }
          .chat-box {
            position: relative;
            box-sizing: border-box;
            display: flex;
            width: 100%;
            height: auto;
            background: rgb(0 0 0 / 50%);
            border-radius: 6px;

            :deep(.custom-textarea) {
              position: relative;
              box-sizing: border-box;
              height: auto;
              padding: 10px;
              font-family: PingFangSC, 'PingFang SC';
              font-size: 16px;
              font-weight: 400;
              color: #fff;
              background: rgb(255 255 255 / 20%);
              border: 1px solid #969799;
              border-radius: 6px;
              backdrop-filter: blur(4px);
              overflow: hidden; /* 隐藏滚动条 */
              resize: none; /* 禁止用户调整大小 */

              /* 隐藏滚动条 */
              &::-webkit-scrollbar {
                display: none;
              }

              /* 占位符样式 */
              &::placeholder {
                color: #cccccc;
              }
            }

            .divider {
              position: absolute;
              right: 61px; /* 竖杠的位置 */
              bottom: 12px;
              width: 1px;
              height: 24px;
              background: #646566;
              border-radius: 1px;
            }

            :deep(.ant-image:nth-of-type(1)) {
              position: absolute;
              right: 74px; /* 第一个 .ant-image 距离右侧 40px */
              bottom: 14px;
              width: 15px;
              height: 20px;
              cursor: pointer;
            }

            :deep(.ant-image:nth-of-type(3)) {
              position: absolute;
              right: 16px; /* 第二个 .ant-image 距离右侧 10px */
              bottom: 8px;
              width: 32px;
              height: 32px;
              cursor: pointer;
            }
          }

          .voice-box {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 48px;
            padding: 0 16px;
            background: rgba(233, 236, 242, 0.7);
            border-radius: 6px;
            backdrop-filter: blur(4px);

            .custom-textarea {
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 100%;

              .exit-recording {
                font-size: 14px;
                font-weight: 500;
                color: #666;
                margin-right: 12px;
              }
              .exit-recording1 {
                font-size: 14px;
                font-weight: 500;
                color: #fff;
                margin-right: 12px;
              }

              .recording-status {
                display: flex;
                align-items: center;
                gap: 8px;

                .waveform {
                  height: 24px;
                  width: auto;
                }

                .recording-text {
                  font-size: 14px;
                  font-weight: 500;
                  color: #1777ff;
                }
              }

              .send-icon {
                width: 32px;
                height: 32px;
                cursor: pointer;
              }
            }
          }
        }
      }
    }

    .model-nmae {
      position: absolute;
      // background: transparent;

      background: #e7efffb5;
      box-shadow: 0px 2px 6px 0px rgba(69, 70, 73, 0.2);
      border-radius: 4px;
      backdrop-filter: blur(4px);

      padding: 9px 12px;
      display: flex;
      justify-content: center;
      top: 20px;
      left: 0;
      > span:nth-child(1) {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        text-align: left;
        font-style: normal;
        margin-right: 8px;
      }
    }
    .model-nmae-option {
      // position: absolute;
      // width: 182px;
      // height: 40px;
      // background: #e7efff;
      // box-shadow: 0px 2px 6px 0px rgba(69, 70, 73, 0.2);
      // border-radius: 4px;
      // opacity: 0.55;
      // top: 20px;
      // left: 0px;
    }
  }

  .model-info {
    color: #000 !important;
    > div:nth-child(1) {
      white-space: nowrap;
      padding: 0 20px 0 0;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
      letter-spacing: 0;
      margin-bottom: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    > div:nth-child(2) {
      margin-bottom: 10px;
    }
    > div:nth-child(3) {
      width: 100%;
      height: 60px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      color: grey;
      letter-spacing: 0;
    }
  }
</style>
