<script setup lang="ts">
  import { ref, reactive, watch, onMounted } from 'vue';
  import { ConfigPackage } from '@/components';
  import { Tooltip, message } from 'ant-design-vue';
  import { QuestionCircleFilled } from '@ant-design/icons-vue';

  import {
    trainMethods,
    parameter,
    general_parameters,
    trainFrameworks,
    trainTypes,
    unsloth_parameters,
    GPUList,
    sklearn_numeric_features_map,
    sklearn_paramters,
  } from './index';
  import { machineLearningList } from '../Manage/index';
  import { getRandomCharacters, makeK8sNameValid } from '@/utils/common';
  import { sourceEnum } from '@/utils/enum';
  import 'ace-builds/src-noconflict/theme-chrome';
  import 'ace-builds/src-noconflict/theme-monokai.js';
  import 'ace-builds/src-noconflict/mode-sh.js';
  import type { Rule } from 'ant-design-vue/es/form/interface';
  import type {
    ITrainFormState,
    General_parameters,
    Train_parameter,
    DataItem,
    IModelFormState,
    ITrainMethodsItem,
    ITrainTypesItem,
    ITrainFrameworksItem,
    IResource,
  } from '@/interface/model';
  import {
    checkSKlearnTarinOutputName,
    checkSklearnModelName,
    checkTarinOutputName,
    checkTrainName,
  } from '@/api/model';
  import { fetchDatasetOptions } from '@/api/dataset';
  import DatasetOptions from './datasetOptions.vue';
  import type { IDatasetOptionItems } from '@/interface/dateset';
  interface IProps {
    model: Partial<IModelFormState>;
  }
  const props = defineProps<IProps>();
  const trainRef = ref();
  interface IDefaultCloseUpDStatus {
    method: boolean;
    setting: boolean;
    dataset: boolean;
    output: boolean;
    resource: boolean;
    parameter: boolean;
    command: boolean;
    childform: Record<string, boolean>;
    general_parameters: boolean;
  }

  const defaultCloseUpDStatus: IDefaultCloseUpDStatus = {
    method: true,
    setting: true,
    dataset: false,
    output: false,
    resource: true,
    parameter: false,
    command: false,
    childform: reactive<Record<string, boolean>>({
      command: true,
      docker: true,
    }),
    general_parameters: false,
  };
  const upStatus = reactive({
    ...defaultCloseUpDStatus,
  });
  // const selectFramework = ref('');
  const frameworks = ref<ITrainFrameworksItem[]>([]);
  const firstMethods = ref<ITrainMethodsItem[]>([]);
  const secondTypes = ref<ITrainTypesItem[]>([]);
  // @ts-expect-error
  const formState: ITrainFormState = reactive({
    model_id: '',
    gpu_number_list: undefined,
    tf_parameter: {
      type: '',
      stage: '',
      finetuning_type: '',
    },
    task_name: '',
    max_run_time: 600,
    dataset_parameter: {
      type: 'private',
      name: '',
    },
    description: '',
    docker_image_id: '',
    output_name: '',
    train_parameter: {
      unsloth_parameters: {
        val_size: 0.25,
        compute_type: 'bf16',
        num_train_epochs: 1,
        learning_rate: '0.00005',
        weight_decay: 0.01,
        per_device_train_batch_size: 1,
        gradient_accumulation_steps: 2,
        warmup_steps: 5,
        lr_scheduler_type: 'cosine',
      },
      general_parameters: {
        learning_rate: '0.00005',
        num_train_epochs: 1,
        max_grad_norm: 1,
        max_samples: 5,
        computed_type: 'bf16',
        cutoff_len: 128,
        per_device_train_batch_size: 1,
        gradient_accumulation_steps: 2,
        val_size: 0.25,
        lr_scheduler_type: 'cosine',
      },
      other_parameters: {
        logging_steps: 5,
        save_steps: 100,
        warmup_steps: 0,
        neftune_noise_alpha: 0,
        // extra_args: '{"optim": "adamw_torch"}',
        packing: false,
        neat_packing: false,
        train_on_prompt: false,
        mask_history: false,
        resize_vocab: false,
        use_llama_pro: false,
      },
      partial_parameters: {
        freeze_trainable_layers: 128,
        freeze_trainable_modules: 'all',
        galore_rank: '',
      },
      LoRA_parameters: {
        lora_rank: 8,
        lora_alpha: 16,
        lora_dropout: 0,
        // loraplus_lr_ratio: 0,
        create_new_adapter: false,
        use_rslora: false,
        use_dora: false,
        // use_pissa: false,
        lora_target: 'all',
        // additional_target: '',
      },
      RLHF_parameters: {
        pref_beta: 0.1,
        pref_ftx: 0,
        pref_loss: 'sigmoid',
        ppo_score_norm: false,
        ppo_whiten_rewards: false,
      },
      GaLore_parameters: {
        use_galore: false,
        galore_rank: 16,
        galore_update_interval: 200,
        galore_scale: 2,
        galore_target: 'all',
      },
      BAdam_parameters: {
        use_badam: false,
        badam_mode: 'layer',
        // adam_switch_mode: 'ascending',
        badam_switch_interval: 50,
        badam_update_ratio: 0.05,
      },
    },
    resource: [{ key: '1', GPU: '1 * NVIDIA V100', CPU: 'Dynamic', memory: 'Dynamic' }],
  });
  const disableState: Record<string, boolean> = reactive({
    other_parameters: false,
    partial_parameters: false,
    LoRA_parameters: false,
    RLHF_parameters: false,
    GaLore_parameters: false,
    BAdam_parameters: false,
  });
  const parameterMap: Record<string, string> = {
    general_parameters: '通用参数设置',
    other_parameters: '其它参数设置',
    partial_parameters: '部分参数微调',
    LoRA_parameters: 'LoRA 参数设置',
    RLHF_parameters: 'RLHF 参数设置',
    GaLore_parameters: 'GaLore 参数设置',
    BAdam_parameters: 'BAdam 参数设置',
  };

  const datasetOptions = ref<IDatasetOptionItems[]>([]);
  const datasetVisible = ref(false);
  interface IState {
    dockerList: Record<string, string>[];
    docker: string;
    dockerType: number;
  }
  const state = reactive<IState>({
    dockerList: [],
    docker: 'reg.shukeyun.com:9088/algorithm/ai-agent-vllm:v2',
    dockerType: 0,
  });

  const selectedRowKeys = ref<(string | number)[]>(['1']);
  const getCheckboxProps = (record: DataItem) => {
    return {
      disabled: record.disabled,
    };
  };
  const rowSelection = reactive({
    checkStrictly: false,
    type: 'radio',
    getCheckboxProps: getCheckboxProps,
    selectedRowKeys,
    onChange: (keys: (string | number)[], selectedRows: DataItem[]) => {
      selectedRowKeys.value = keys;
      const items: IResource[] = JSON.parse(JSON.stringify(selectedRows));
      formState.resource = items;
    },
  });
  const resourceColumn = [
    {
      title: 'GPU',
      dataIndex: 'GPU',
      key: 'GPU',
    },
    {
      title: 'CPU',
      dataIndex: 'CPU',
      key: 'CPU',
    },
    {
      title: '内存',
      dataIndex: 'memory',
      key: 'memory',
    },
  ];

  const validateFields = () => {
    return new Promise(async (resolve) => {
      try {
        await trainRef.value.validateFields();
        resolve(true);
      } catch (e: unknown) {
        // @ts-expect-error
        message.warn(e.errorFields[0].errors);
      }
    });
  };
  const closeUpStatus = (
    key: keyof typeof defaultCloseUpDStatus,
    notClose: (keyof typeof defaultCloseUpDStatus)[] = [],
    childKeys: string[] = [],
  ) => {
    const temp = { ...defaultCloseUpDStatus };
    if (typeof temp[key] == 'boolean') {
      // @ts-expect-error
      temp[key] = !upStatus[key];
    } else {
      //子项套多个子项
      if (childKeys)
        childKeys.map((e) => {
          if (!Object.keys(temp[key]).includes(e))
            // @ts-expect-error
            temp[key][e] = true; //赋默认值
          // @ts-expect-error
          else temp[key][e] = !temp[key][e];
        });
    }
    //忽略父项
    for (const key of notClose) {
      // @ts-expect-error
      temp[key] = true;
    }
    Object.assign(upStatus, temp);
  };

  const getDatasetOptions = async (source: string, type: string) => {
    const data: IDatasetOptionItems[] = await fetchDatasetOptions(source, type);
    datasetOptions.value = data;
  };
  watch(
    () => props.model,
    async (model) => {
      if (model) {
        const { source_name, tf, id, docker_images } = model;
        // 回显配置好的镜像
        state.dockerList = docker_images!;
        if (docker_images!.length) formState.docker_image_id = state.dockerList[0].id;

        formState.model_id = id!;
        const customName = `${makeK8sNameValid(`${source_name}_${getRandomCharacters()}`)}`;
        formState.task_name = customName;
        formState.output_name = customName;

        if (machineLearningList.includes(model.category!)) {
          // @ts-expect-error
          formState.train_parameter = sklearn_paramters[props.model.category!].value;
        }
        if (!['p2l'].includes(model.category!)) {
          // 训练方式联动
          const frameworkKeys = Object.keys(tf || {});
          // 根据模型训练方式回显可选的框架
          frameworks.value = trainFrameworks.filter((item) => {
            return frameworkKeys.includes(item.value);
          });
          // 默认选中第一个框架
          const selectFramework = frameworks.value.length ? frameworks.value[0].value : '';
          formState.tf_parameter.type = selectFramework;
          // 找到默认框架下的所有方法并回显
          const methods = Object.keys(tf![selectFramework]);
          firstMethods.value = trainMethods.filter((item) => methods.includes(item.value));
          formState.tf_parameter.stage = methods.length ? methods[0] : '';

          // @ts-expect-error
          const types: string[] = tf![formState.tf_parameter.type][formState.tf_parameter.stage];
          secondTypes.value = trainTypes.filter((item) => types.includes(item.value));
          formState.tf_parameter.finetuning_type = types.length ? types[0] : '';
        }
      }
      if (formState.dataset_parameter) {
        // @ts-expect-error
        getDatasetOptions(formState.dataset_parameter.type, 'sft');
      }
    },
    { deep: true, immediate: true },
  );

  const validatorName = async (_rule: Rule, value: string) => {
    if (value == '' || value.trim().length == 0) {
      return Promise.reject('请输入服务名称');
    }
    const regex = /^[a-z0-9][a-z0-9\-.]*[a-z0-9]$/;
    if (!regex.test(value)) {
      return Promise.reject('格式不正确：仅支持小写字母、数字、-和.，且首尾必须为字母或数字');
    }
    if (value.length > 50) {
      return Promise.reject('任务名称最多输入 50 个字');
    }
    try {
      machineLearningList.includes(props.model.category!)
        ? await checkSklearnModelName(value.trim())
        : await checkTrainName(value.trim());
    } catch (e) {
      if (e === 'AlreadyExists') {
        return Promise.reject('该名称已存在，请重新命名');
      }
      return Promise.reject(e);
    }
    return Promise.resolve();
  };

  const validatorModelName = async (_rule: Rule, value: string) => {
    if (value == '' || value.trim().length == 0) {
      return Promise.reject('请输入服务名称');
    }
    const regex = /^[a-z0-9][a-z0-9\-.]*[a-z0-9]$/;
    if (!regex.test(value)) {
      return Promise.reject('格式不正确：仅支持小写字母、数字、-和.，且首尾必须为字母或数字');
    }
    if (value.length > 50) {
      return Promise.reject('模型名称最多输入 50 个字');
    }
    try {
      machineLearningList.includes(props.model.category!)
        ? await checkSKlearnTarinOutputName(value.trim())
        : await checkTarinOutputName(value.trim());
    } catch (e) {
      if (e === 'AlreadyExists') {
        return Promise.reject('该名称已存在，请重新命名');
      }
      return Promise.reject(e);
    }
    return Promise.resolve();
  };

  const handleChangeDataSet = (e: string) => {
    if (!machineLearningList.includes(props.model.category!)) {
      formState.dataset_parameter.name = e === 'system' ? ['identity'] : ['travel_plan'];
    }
  };

  const filterOption = (input: string, option: { id: string; name: string }) => {
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };
  const handleChangeFramework = (type: string) => {
    formState.tf_parameter.type = type;
    const methods = Object.keys(props.model.tf![type]);
    formState.tf_parameter.stage = methods[0] || '';
    firstMethods.value = trainMethods.filter((item) => methods.includes(item.value));
    // @ts-expect-error
    const types = props.model.tf![type][formState.tf_parameter.stage];
    secondTypes.value = trainTypes.filter((item) => types.includes(item.value));
    formState.tf_parameter.finetuning_type = types[0] || '';
  };

  const confirmOk = async (record: IDatasetOptionItems) => {
    Object.assign(formState.dataset_parameter, { ...record });
    await trainRef.value.clearValidate();
  };
  onMounted(async () => {});
  defineExpose({
    validateFields,
    formState,
    disableState,
  });

  watch(
    () => formState.dataset_parameter.type,
    (type) => {
      if (type) {
        // @ts-expect-error
        getDatasetOptions(type, 'sft');
      }
    },
    { deep: true },
  );
</script>

<template>
  <a-form ref="trainRef" :model="formState" name="basic" autocomplete="off" layout="vertical">
    <ConfigPackage :expand="upStatus.setting" :expand-click="() => closeUpStatus('setting')" label="训练设置">
      <a-form-item label="任务名称" name="task_name" :rules="[{ required: true, validator: validatorName }]">
        <a-input v-model:value="formState.task_name" placeholder="请输入任务名称" show-count :maxlength="50" />
      </a-form-item>
      <a-form-item label="最大运行时长" name="max_run_time">
        <a-input-number
          v-model:value="formState.max_run_time"
          :precision="0"
          :min="0"
          placeholder="请输入最大运行时长"
          :style="{ width: '100%' }"
        >
          <template #addonAfter> 秒 </template>
        </a-input-number>
      </a-form-item>
    </ConfigPackage>

    <ConfigPackage
      v-if="!['p2l', ...machineLearningList].includes(model.category!)"
      :expand="upStatus.method"
      :expand-click="() => closeUpStatus('method')"
      label="训练方式"
    >
      <div>
        <div class="mb-10px">训练框架</div>
        <div class="types">
          <template v-for="item in frameworks" :key="item.value">
            <div
              class="types-items"
              :class="{ active: formState.tf_parameter.type === item.value }"
              @click="handleChangeFramework(item.value)"
            >
              <div class="flex-col">
                <div class="font-bold">{{ item.name }}</div>
                <div class="c-#7f7f7f font-size-14px">{{ item.desc }}</div>
              </div>
              <div v-if="formState.tf_parameter.type === item.value" class="checkbox">
                <a-checkbox :checked="true"></a-checkbox>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div>
        <div class="mb-10px">训练方法</div>
        <div class="flex-nowrap flex overflow-x-auto whitespace-nowrap overflow-scroll p-x-10px">
          <div
            v-for="item in firstMethods"
            :key="item.value"
            class="radio-button-wrappe flex p-10px"
            :class="{ actived: formState.tf_parameter.stage === item.value }"
            @click="
              formState.tf_parameter.stage = item.value;
              formState.tf_parameter.finetuning_type = 'lora';
            "
          >
            <span class="mr-5px">{{ item.name }}</span>
            <Tooltip>
              <template #title>
                {{ item.desc }}
              </template>
              <QuestionCircleFilled />
            </Tooltip>
          </div>
        </div>
        <div class="types">
          <template v-for="item in secondTypes" :key="item.value">
            <div
              class="types-items"
              :class="{ active: formState.tf_parameter.finetuning_type === item.value }"
              @click="formState.tf_parameter.finetuning_type = item.value"
            >
              <div class="flex-col">
                <div class="font-bold">{{ item.label }}</div>
                <div class="c-#7f7f7f font-size-14px">{{ item.desc }}</div>
              </div>
              <div v-if="formState.tf_parameter.finetuning_type === item.value" class="checkbox">
                <a-checkbox :checked="true"></a-checkbox>
              </div>
            </div>
          </template>
        </div>
      </div>
    </ConfigPackage>

    <!-- 机器学习数据集配置 -->
    <template v-if="model.category !== 'p2l'">
      <ConfigPackage
        v-if="machineLearningList.includes(model.category!)"
        :expand="upStatus.dataset"
        :expand-click="() => closeUpStatus('dataset')"
        label="数据集配置"
      >
        <a-row :style="{ marginBottom: '10px' }">
          <a-col :span="6">
            <a-select
              v-model:value="formState.dataset_parameter.type"
              style="width: 100%"
              @change="handleChangeDataSet"
            >
              <a-select-option value="system">公开数据集</a-select-option>
              <a-select-option value="private">自研数据集</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="18">
            <div class="dataset">
              <a-form-item
                label=""
                :name="['dataset_parameter', 'name']"
                :rules="[{ required: true, message: '请选择数据集' }]"
                :style="{ marginBottom: '0px' }"
              >
                <a-select
                  v-model:value="formState.dataset_parameter.name"
                  style="width: 100%"
                  placeholder="请选择数据集"
                >
                  <a-select-option value="IndividualCreditRatingForm">
                    <div>IndividualCreditRatingForm</div>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </div>
          </a-col>
        </a-row>
        <a-form-item
          label="数值特征"
          :name="['dataset_parameter', 'numeric_features']"
          :rules="[{ required: true, message: '请选择数值特征' }]"
          :style="{ marginBottom: '0px' }"
        >
          <a-select
            v-model:value="formState.dataset_parameter.numeric_features"
            mode="multiple"
            style="width: 100%"
            placeholder="请选择数值特征"
          >
            <a-select-option
              v-for="item in sklearn_numeric_features_map[model.category!]"
              :key="item.value"
              :value="item.value"
            >
              <div>{{ item.label }}</div>
            </a-select-option>
          </a-select>
        </a-form-item>
        <!-- kmeans 没有数据标签 -->
        <a-form-item
          v-if="model.category !== 'sklearn-KMeans'"
          label="数值标签"
          :name="['dataset_parameter', 'numeric_labels']"
          :rules="[{ required: true, message: '请选择数值标签' }]"
          :style="{ marginBottom: '0px' }"
        >
          <a-select
            v-model:value="formState.dataset_parameter.numeric_labels"
            style="width: 100%"
            placeholder="请选择数值标签"
          >
            <a-select-option
              v-for="item in sklearn_numeric_features_map[model.category!]"
              :key="item.value"
              :value="item.value"
            >
              <div>{{ item.label }}</div>
            </a-select-option>
          </a-select>
        </a-form-item>
      </ConfigPackage>
      <ConfigPackage
        v-else
        :expand="upStatus.dataset"
        :expand-click="() => closeUpStatus('dataset')"
        label="数据集配置"
      >
        <a-row :style="{ marginBottom: '10px' }">
          <a-col :span="6">
            <a-select v-model:value="formState.dataset_parameter.type" style="width: 100%">
              <a-select-option value="system">公开数据集</a-select-option>
              <a-select-option value="private">我的数据集</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="18">
            <div class="dataset">
              <a-form-item
                label=""
                :name="['dataset_parameter', 'name']"
                :rules="[{ required: true, message: '请选择数据集' }]"
                :style="{ marginBottom: '0px' }"
              >
                <div
                  class="dataset-options"
                  :style="{ color: formState.dataset_parameter.name ? '#000' : '#ccc' }"
                  @click="datasetVisible = true"
                >
                  {{ formState.dataset_parameter.name || '请选择数据集' }}
                </div>
              </a-form-item>
            </div>
          </a-col>
        </a-row>
      </ConfigPackage>
    </template>
    <ConfigPackage :expand="upStatus.output" :expand-click="() => closeUpStatus('output')" label="输出配置">
      <a-form-item label="模型名称" name="output_name" :rules="[{ required: true, validator: validatorModelName }]">
        <a-input v-model:value="formState.output_name" placeholder="请输入任务名称" show-count :maxlength="50" />
      </a-form-item>
      <a-form-item label="版本描述" name="description">
        <a-textarea v-model:value="formState.description" placeholder="请输入版本描述" :rows="4" />
      </a-form-item>
    </ConfigPackage>

    <ConfigPackage :expand="upStatus.resource" :expand-click="() => closeUpStatus('resource')" label="计算资源配置">
      <ConfigPackage
        v-if="!machineLearningList.includes(model.category!)"
        :expand="upStatus.childform['resourceList']"
        :expand-click="() => closeUpStatus('childform', ['resource'], ['resourceList'])"
        label="资源配置"
        spacious
      >
        <a-form-item label="GPU 显卡" name="gpu_number_list" :rules="[{ required: true }]">
          <a-select v-model:value="formState.gpu_number_list" placeholder="请选择" style="width: 100%">
            <a-select-option v-for="n in GPUList" :key="n.value" :value="n.value">{{ n.label }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="资源列表" :rules="[{ required: true }]">
          <a-table
            :data-source="sourceEnum"
            :columns="resourceColumn"
            :row-selection="rowSelection"
            row-key="key"
            :pagination="false"
          />
        </a-form-item>
      </ConfigPackage>
      <ConfigPackage
        :expand="upStatus.childform['docker']"
        :expand-click="() => closeUpStatus('childform', ['resource'], ['docker'])"
        label="镜像配置"
        spacious
      >
        <a-form-item label="镜像选择" name="docker_image_id" :rules="[{ required: true, message: '请选择镜像' }]">
          <a-select
            v-model:value="formState.docker_image_id"
            show-search
            :options="state.dockerList"
            :field-names="{ label: 'name', value: 'id' }"
            :filter-option="filterOption"
            placeholder="请选择"
            style="width: 100%"
          >
          </a-select>
        </a-form-item>
      </ConfigPackage>
      <!-- <ConfigPackage
          :expand="upStatus.childform['command']"
          :expand-click="() => closeUpStatus('childform', ['resource'], ['command'])"
          label="初始命令"
          tip="与容器相关的初始命令"
          spacious
        >
          <v-ace-editor
            v-model:value="formState.run_command"
            lang="sh"
            theme="monokai"
            :options="aceOptions"
            style="height: 300px; border: 1px solid #dbd3d3"
          />
        </ConfigPackage> -->
    </ConfigPackage>

    <ConfigPackage
      v-if="!['sklearn-linearRegression', 'p2l'].includes(model.category!)"
      :expand-click="() => closeUpStatus('parameter')"
      :expand="upStatus.parameter"
      label="超参数配置"
    >
      <div v-if="machineLearningList.includes(model.category!)" class="parameter">
        <div
          v-for="item in sklearn_paramters[model.category as string].params"
          :key="item.value"
          class="parameter-items"
        >
          <a-form-item
            :name="['train_parameter', item.value]"
            :rules="[
              {
                required: item.required,
                message: ['input', 'inputNumber'].includes(item.type) ? '请输入' : '请选择',
              },
            ]"
          >
            <template #label>
              <span>{{ item.label }}</span>
              <Tooltip v-if="item.desc">
                <template #title>{{ item.desc }}</template>
                <QuestionCircleFilled class="ml-5px" />
              </Tooltip>
            </template>
            <a-input
              v-if="item.type === 'input'"
              v-model:value="formState.train_parameter[`${item.value as keyof General_parameters}`]"
              :default-value="item.default"
              placeholder="请输入"
            />
            <a-input-number
              v-if="item.type === 'inputNumber'"
              v-model:value="formState.train_parameter[`${item.value as keyof General_parameters}`]"
              :default-value="item.default"
              :min="item.min"
              :max="item.max"
              :step="item.step || 1"
              :precision="item.precision || 0"
              :style="{ width: '100%' }"
              placeholder="请输入"
            ></a-input-number>
            <a-select
              v-if="item.type === 'select'"
              v-model:value="formState.train_parameter[`${item.value as keyof General_parameters}`]"
              :default-value="item.default"
              style="width: 100%"
            >
              <a-select-option v-for="opt in item.option" :key="opt.value" :value="opt.value">{{
                opt.label
              }}</a-select-option>
            </a-select>
            <a-checkbox
              v-if="item.type === 'checkbox'"
              v-model:checked="formState.train_parameter[`${item.value as keyof General_parameters}`]"
            ></a-checkbox>
          </a-form-item>
        </div>
      </div>
      <template v-else>
        <div v-if="formState.tf_parameter.type === 'unsloth'" class="parameter">
          <div v-for="item in unsloth_parameters" :key="item.value" class="parameter-items">
            <a-form-item
              :name="['train_parameter', 'unsloth_parameters', item.value]"
              :rules="[
                {
                  required: item.required,
                  message: ['input', 'inputNumber'].includes(item.type) ? '请输入' : '请选择',
                },
              ]"
            >
              <template #label>
                <span>{{ item.label }}</span>
                <Tooltip v-if="item.desc">
                  <template #title>{{ item.desc }}</template>
                  <QuestionCircleFilled class="ml-5px" />
                </Tooltip>
              </template>
              <a-input
                v-if="item.type === 'input'"
                v-model:value="
                  formState.train_parameter.unsloth_parameters[`${item.value as keyof General_parameters}`]
                "
                placeholder="请输入"
              />
              <a-input-number
                v-if="item.type === 'inputNumber'"
                v-model:value="
                  formState.train_parameter.unsloth_parameters[`${item.value as keyof General_parameters}`]
                "
                :min="item.min"
                :max="item.max"
                :step="item.step || 1"
                :precision="item.precision || 0"
                :style="{ width: '100%' }"
                placeholder="请输入"
              ></a-input-number>
              <a-select
                v-if="item.type === 'select'"
                v-model:value="
                  formState.train_parameter.unsloth_parameters[`${item.value as keyof General_parameters}`]
                "
                style="width: 100%"
              >
                <a-select-option v-for="opt in item.option" :key="opt.value" :value="opt.value">{{
                  opt.label
                }}</a-select-option>
              </a-select>
              <a-checkbox
                v-if="item.type === 'checkbox'"
                v-model:checked="
                  formState.train_parameter.unsloth_parameters[`${item.value as keyof General_parameters}`]
                "
              ></a-checkbox>
            </a-form-item>
          </div>
        </div>
        <template v-else>
          <ConfigPackage
            :expand="upStatus.childform['general_parameters']"
            :expand-click="() => closeUpStatus('childform', ['parameter'], ['general_parameters'])"
            label="通用参数设置"
          >
            <div>
              <div class="parameter">
                <div v-for="item in general_parameters" :key="item.value" class="parameter-items">
                  <a-form-item
                    :name="['train_parameter', 'general_parameters', item.value]"
                    :rules="[
                      {
                        required: item.required,
                        message: ['input', 'inputNumber'].includes(item.type) ? '请输入' : '请选择',
                      },
                    ]"
                  >
                    <template #label>
                      <span>{{ item.label }}</span>
                      <Tooltip v-if="item.desc">
                        <template #title>{{ item.desc }}</template>
                        <QuestionCircleFilled class="ml-5px" />
                      </Tooltip>
                    </template>
                    <a-input
                      v-if="item.type === 'input'"
                      v-model:value="
                        formState.train_parameter.general_parameters[`${item.value as keyof General_parameters}`]
                      "
                      placeholder="请输入"
                    />
                    <a-input-number
                      v-if="item.type === 'inputNumber'"
                      v-model:value="
                        formState.train_parameter.general_parameters[`${item.value as keyof General_parameters}`]
                      "
                      :min="item.min"
                      :max="item.max"
                      :step="item.step || 1"
                      :precision="item.precision || 0"
                      :style="{ width: '100%' }"
                      placeholder="请输入"
                    ></a-input-number>
                    <a-select
                      v-if="item.type === 'select'"
                      v-model:value="
                        formState.train_parameter.general_parameters[`${item.value as keyof General_parameters}`]
                      "
                      style="width: 100%"
                    >
                      <a-select-option v-for="opt in item.option" :key="opt.value" :value="opt.value">{{
                        opt.label
                      }}</a-select-option>
                    </a-select>
                    <a-checkbox
                      v-if="item.type === 'checkbox'"
                      v-model:checked="
                        formState.train_parameter.general_parameters[`${item.value as keyof General_parameters}`]
                      "
                    ></a-checkbox>
                  </a-form-item>
                </div>
              </div>
            </div>
          </ConfigPackage>
          <ConfigPackage
            v-for="key in Object.keys(parameter)"
            :key="key"
            v-model:on="disableState[key]"
            :expand="upStatus.childform[key]"
            :expand-click="() => closeUpStatus('childform', ['parameter'], [key])"
            :label="parameterMap[key]"
          >
            <div>
              <div class="parameter">
                <div v-for="item in parameter[key]" :key="item.value" class="parameter-items">
                  <a-form-item
                    :name="['train_parameter', key, item.value]"
                    :rules="[
                      {
                        required: item.required,
                        message: ['input', 'inputNumber'].includes(item.type) ? '请输入' : '请选择',
                      },
                    ]"
                  >
                    <template #label>
                      <span>{{ item.label }}</span>
                      <Tooltip v-if="item.desc">
                        <template #title>{{ item.desc }}</template>
                        <QuestionCircleFilled class="ml-5px" />
                      </Tooltip>
                    </template>
                    <a-input
                      v-if="item.type === 'input'"
                      v-model:value="
                        formState.train_parameter[key as keyof Train_parameter][
                          `${item.value}` as keyof General_parameters
                        ]
                      "
                      placeholder="请输入"
                    />
                    <a-input-number
                      v-if="item.type === 'inputNumber'"
                      v-model:value="formState.train_parameter[key][`${item.value}`]"
                      :min="item.min"
                      :max="item.max"
                      :step="item.step || 1"
                      :precision="item.precision || 0"
                      :style="{ width: '100%' }"
                      placeholder="请输入"
                    ></a-input-number>
                    <a-select
                      v-if="item.type === 'select'"
                      v-model:value="formState.train_parameter[key][`${item.value}`]"
                      style="width: 100%"
                    >
                      <a-select-option v-for="opt in item.option" :key="opt.value" :value="opt.value">{{
                        opt.label
                      }}</a-select-option>
                    </a-select>
                    <a-checkbox
                      v-if="item.type === 'checkbox'"
                      v-model:checked="formState.train_parameter[key][`${item.value}`]"
                    ></a-checkbox>
                  </a-form-item>
                </div>
              </div>
            </div>
          </ConfigPackage>
        </template>
      </template>
    </ConfigPackage>
  </a-form>

  <DatasetOptions v-model:visible="datasetVisible" :dataSource="datasetOptions" @ok="confirmOk" />
</template>

<style scoped lang="less">
  @import url('./index.less');

  .parameter {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: space-between;
    padding: 10px;

    .parameter-items {
      flex: 1 1 49%;
      max-width: 49%;
    }
  }

  .dataset {
    :deep(.ant-form-item) {
      margin-bottom: 0 !important;
    }
  }
  .dataset-options {
    width: 100%;
    background-color: #fff;
    border-radius: 5px;
    height: 32px;
    line-height: 32px;
    padding-left: 5px;
    border: 1px solid #d9d9d9;
    &:hover {
      border-color: #4096ff;
    }
  }
</style>
