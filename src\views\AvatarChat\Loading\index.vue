<script setup lang="ts">
  import { SyncOutlined } from '@ant-design/icons-vue';
  import Icon from '@/components/Icon/index.vue';

  const props = defineProps({
    state: {
      type: [String, Object],
      default: '',
    },
    name: {
      // 新增
      type: String,
      default: '',
    },
    from: {
      type: String,
      default: '',
    },
    handleDelete: {
      type: Function,
      default: () => {},
    },
    handleReGenerate: {
      type: Function,
      default: () => {},
    },
  });
</script>

<template>
  <div
    :class="[
      'loading',
      props.from === 'digitalImage' ? 'digitalImageLoading' : '',
      props.from === 'rolesChange' ? 'rolesChangeLoading' : '',
    ]"
  >
    <template v-if="!props.state">
      <span v-if="typeof state === 'string'" class="text">{{ props.state }}</span>
      <span v-else>{{ props.state }}</span>
    </template>
    <template v-else>
      <div v-if="['数字人生成失败，请重新生成', '声⾳⽣成失败，请重新⽣成'].includes(props.state)" class="fail">
        <div class="img-container">
          <Icon name="jinggao" :size="28" />
          <p class="fail-text">{{ props.state }}</p>
        </div>
        <div class="fail-btn">
          <a-button class="btn-item btn-general" @click="props.handleReGenerate"><span>重新生成</span></a-button>
          <a-popconfirm
            :title="`确定删除数字人 “${props.name}”？`"
            ok-text="确定"
            cancel-text="取消"
            @confirm="props.handleDelete"
          >
            <template #description>
              <div style="color: #999">删除后不可恢复</div>
            </template>
            <a-button class="fail-btn-item btn-general">删除</a-button>
          </a-popconfirm>
        </div>
      </div>

      <div v-else>
        <SyncOutlined class="sync-icon" spin />
        <p class="text">{{ props.state }}</p>
      </div>
    </template>
  </div>
</template>

<style lang="less" scoped>
  .loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 134px;
    height: 128px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    color: #fff;
    // font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    .text {
      font-size: 14px;
    }
    .sync-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      width: 32px;
    }

    .fail {
      .img-container {
        width: 134px;
        height: 85px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        flex-direction: column;
        padding-top: 15px;
        .fail-text {
          width: 70%;
          font-size: 12px;
          margin-top: 4px;
          text-align: center;
          color: #ffffff;
          line-height: 17px;
        }
      }

      .fail-btn {
        padding: 0 10px;
        gap: 8px;
        // margin: 0;
        width: 100%;
        display: flex;
        align-items: center;
        .btn-general {
          height: 24px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .fail-btn-item {
          // 删除按钮
          border: 1px solid #999999;
          background: #646464;
          color: #fff;
          font-size: 12px;
          color: #ffffff;
          line-height: 17px;
          text-align: center;
          width: 40px;
          padding: 4px 8px;
        }

        .btn-item {
          // 重新生成按钮
          // padding: 3px 8px;
          width: 64px;
          height: 24px;
          background: #ffffff;
          border-radius: 4px;
          span {
            height: 18px;
            font-size: 12px;
            color: #17181a;
            line-height: 18px;
            text-align: center;
          }

          :deep(.ant-btn-default) {
            background: #999999;
            color: #fff;
          }
        }
      }
    }

    :deep(.ant-popover-message-icon) {
      display: none !important;
    }
  }

  .digitalImageLoading {
    width: 178px;
    height: 238px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 8px;
  }

  .rolesChangeLoading {
    height: 176px;
  }
</style>
