<script setup lang="ts">
  import { ref, onMounted, reactive, nextTick, watch, computed } from 'vue';
  import sendBtn from '@/assets/image/base/pictures/sendbtn.png';
  import sendHover from '@/assets/image/base/pictures/sendHover.png.png';
  import sendDisable from '@/assets/image/base/pictures/sendDisable.png';

  import TextToImg from '@/assets/image/base/pictures/textToImg.png';
  // import download from '@/assets/image/base/pictures/download.png';
  import { WebSocketClient as webSocketClass } from '@/utils/ws';
  import config from '@/config';
  import { getLocalItem } from '@/utils/common';
  import { useThrottle } from '@/hooks/useThrottle';
  import { message } from 'ant-design-vue';
  import { InfoCircleOutlined } from '@ant-design/icons-vue';
  import { getModelList } from '@/api/textToImage';
  import { DownloadOutlined } from '@ant-design/icons-vue';

  interface Message {
    type: 'question' | 'answer';
    text: string;
    answer?: string;
    picture_url?: string;
    icon?: string;
    invalidText?: string;
  }
  interface ResponseData {
    picture_url?: string;
    code: number;
    answer?: string;
  }

  const defaultText = '帮我画一张有老人、狗、海边、惬意生活的图片';
  const btnImageSrc = computed(() => {
    let flag = sendBtn;
    if (inputText.value) {
      flag = loading.value ? sendDisable : sendBtnHover.value ? sendHover : sendBtn;
    } else {
      flag = sendDisable;
    }
    return flag;
  });
  // 输入框内容
  const inputText = ref(defaultText);
  let hasErrorMessage = false; // 错误消息标志位
  const loading = ref(false); // 图片加载标志位
  const sendBtnHover = ref(false);
  const msgBoxRef = ref<any>(undefined);
  // 消息列表
  const state = reactive({
    messageList: [
      {
        type: 'answer',
        text: '你好，欢迎使用文生图功能，输入你的描述，让我们一起创造属于我们的图像世界吧，你可以发送输入框的内容试一下',
      },
    ] as Message[], // 消息列表
  });

  const modelList = ref<any>([]);
  const activeModel = ref<any>(null);

  // 创建 WebSocket 实例
  const textToImageWs = new webSocketClass(config.textToImage, (data: ResponseData) => {
    getMsgAndVideoList(data);
  });

  const getParams = (param: any) => {
    const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');
    return {
      user_id: userId,
      model: 'shuke-8b-dpo',
      topic_id: '4e11fd31-99ac-452a-92e8-4db348669a75',
      question: inputText.value,
      ...param,
    };
  };

  const getMsgAndVideoList = (data: ResponseData) => {
    const { picture_url, code, answer } = data;
    const isError = [403, 500, 501, 502, 503, 505, 506, 508, 510].includes(code);

    if (isError) {
      if (!hasErrorMessage) {
        state.messageList.push({
          type: 'answer',
          text: '没能明白您的意思，请重新提问~',
        });
        hasErrorMessage = true; // 设置标志位为 true
      }

      return;
    }

    if (code === 200) {
      loading.value = true; // 重置标志位
      state.messageList.push({
        type: 'answer',
        text: '正在绘制生成',
        icon: 'loading',
      });
    }

    if (code === 201) {
      // 检测最新一条数据是否包含 icon 字段
      const lastMessage = state.messageList[state.messageList.length - 1];
      if (lastMessage && lastMessage.icon) {
        // 如果包含 icon 字段，将其从消息列表中删除
        state.messageList.pop();
      }
      // 将回答加入消息列表
      if (picture_url) {
        if (picture_url === 'fail to create image') {
          state.messageList.push({
            type: 'answer',
            text: '图片生成失败，稍后再试吧',
          });
        } else {
          state.messageList.push({
            type: 'answer',
            text: '已根据你的描述生成图片，快点击查看吧',
            picture_url: picture_url,
          });
        }
      } else {
        if (answer) {
          const lastMessage = state.messageList.slice(-1)[0];
          if (lastMessage && lastMessage.type === 'answer') {
            // 如果最后一条消息是回答，追加内容
            lastMessage.invalidText += answer;
          } else {
            // 否则新增一条回答
            state.messageList.push({
              type: 'answer',
              text: '你的描述和图片生成无关，我只能处理和图片相关的问题，请重新输入有关图片的描述试试吧',
              invalidText: answer,
            });
          }
        }
      }
    }

    if (code === 202) {
      loading.value = false; // 重置图片加载标志位
      hasErrorMessage = false; // 重置错误消息标志位
    }
  };

  const setInputValue = (value: string) => {
    inputText.value = value;
  };

  // 发送消息
  const sendMessage = useThrottle((text?: string) => {
    const value = typeof text === 'string' ? text : inputText.value;
    if (!value || !value.trim()) {
      message.warning('请输入描述内容');
      return;
    }
    const question = text || inputText;

    setTimeout(() => {
      inputText.value = '';
    }, 0);
    loading.value = true; // 设置加载状态为 true

    // 将提问加入消息列表
    state.messageList.push({
      type: 'question',
      text: typeof question === 'string' ? question : question.value,
    });

    textToImageWs?.send(getParams({ question: question }));
  }, 3000);

  const downloadImage = async (picture_url: string) => {
    const imageURL = picture_url;
    const response = await fetch(imageURL);
    const blob = await response.blob();
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'downloaded_image.jpg'; // 为下载的图片指定一个文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  watch(state.messageList, () => {
    nextTick(() => {
      // (msgBoxRef.value as HTMLElement).scrollTo({ left: 0, top: 10000 });
      (msgBoxRef.value as HTMLElement).scrollTo({ left: 0, top: msgBoxRef.value.scrollHeight });
    });
  });

  // 页面加载时初始化
  onMounted(async () => {
    textToImageWs.connect();

    const models = await getModelList();
    modelList.value = models?.['image-text-to-image'];
    activeModel.value = modelList.value[0];
  });
</script>
<template>
  <div class="text-to-image-container">
    <!-- 消息框 -->
    <div ref="msgBoxRef" class="msg-box">
      <template v-for="(item, i) in state.messageList" :key="i">
        <!-- 问题展示 -->
        <div v-if="item.type === 'question'" class="question-box">
          <span class="text">{{ item.text }}</span>
        </div>
        <!-- 回答展示 -->
        <div v-else-if="item.type === 'answer'" class="answer-box">
          <div class="text-right">
            <div style="display: flex; justify-content: flex-start; align-items: center">
              <template v-if="item.icon === 'loading'">
                <div class="loader" style="margin-right: 10px"></div>
              </template>
              <span class="text-15px">{{ item.text }}</span>
            </div>
            <div v-if="item.picture_url" class="image-container mt-12px">
              <!-- <span>{{ item.text }}</span> -->
              <a-image style="height: 320px; width: 320px" class="AImg" :src="item.picture_url" alt="" />
              <div class="download-btn" @click="downloadImage(item.picture_url)">
                <DownloadOutlined style="fontsize: 24px" />
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <!-- <InfoCircleOutlined class="info-icon" /> -->
    <!-- 聊天框 -->
    <div class="chat-box">
      <div class="tip-box">
        <div class="tip-icon">
          <a-image :src="TextToImg" alt="" :preview="false" />
        </div>
        <span class="title">文生图</span>
        <span class="divider"> | </span>
        <span class="text">描述你想象的图片、角色、场景···AI帮你画图</span>
      </div>
      <div class="input-box">
        <a-textarea
          v-model:value="inputText"
          class="custom-textarea"
          placeholder="输入你的描述，继续创作图片"
          style="color: #17181a"
          :disabled="loading"
          :auto-size="{ minRows: 1, maxRows: 4 }"
          @change="
            (e: any) => {
              setInputValue(e.target.value);
            }
          "
          @press-enter="
            (e: any) => {
              e.preventDefault();
              sendMessage(inputText);
            }
          "
        />
        <div class="send-button" @mouseenter="sendBtnHover = true" @mouseleave="sendBtnHover = false">
          <a-image
            :src="btnImageSrc"
            alt=""
            :preview="false"
            :style="{ width: '32px', height: '32px', cursor: loading ? 'not-allowed' : 'pointer' }"
            @click="sendMessage(inputText)"
          />
        </div>
      </div>
    </div>

    <div class="model-nmae">
      <span>{{ activeModel?.name || 'black-forest-labs/FLUX.1-dev' }}</span>
      <a-tooltip placement="bottom" color="#fff" :overlayInnerStyle="{ width: '320px' }">
        <template #title v-if="activeModel"><slot name="modelInfo" :value="activeModel"></slot></template>
        <InfoCircleOutlined class="info-icon" style="color: rgba(0, 0, 0, 0.35); cursor: pointer" />
      </a-tooltip>
    </div>
  </div>
</template>
<style lang="less" scoped>
  .text-to-image-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    .msg-box {
      width: 60%;
      // padding: 0 10px;
      margin-top: 24px;
      height: 100%;
      overflow-y: auto;

      .answer-box {
        width: 100%;
        display: flex;
        text-align: start;

        .text-right {
          max-width: 80%;
          padding: 10px 15px;
          display: inline-block;
          text-align: left;
          border: 1px solid #ebebeb;
          box-shadow: 0px 4px 8px 0px rgba(216, 227, 243, 0.25);
          border-radius: 12px 12px 12px 0px;

          .image-container {
            position: relative;
            display: flex;
            align-items: end;
            width: 100%;

            .AImg {
              width: 320px;
              height: 320px;
              padding: 16px 20px;
              border-radius: 8px;
              object-fit: cover; /* 确保图片适应容器 */
            }
            .download-btn {
              cursor: pointer;
              color: #797979;
              padding-left: 12px;
              border-radius: 8px;
              &:hover {
                color: #000;
              }
            }
          }

          > span {
            font-size: 15px;
            font-weight: 400;
            font-family:
              PingFangSC,
              PingFang SC;
            color: #17181a;
            line-height: 24px;

            font-style: normal;
          }
        }
      }

      .question-box {
        margin: 24px 0;
        width: 100%;
        display: flex;
        flex-direction: row-reverse;
        text-align: start;

        > span {
          max-width: 90%;
          padding: 10px 15px;
          font-size: 15px;
          display: inline-block;
          background: #dceefd;
          border-radius: 12px 12px 0px 12px;
          border: 1px solid #ffffff;
          opacity: 0.9;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          color: #17181a;
          line-height: 24px;
          text-align: justify;
          font-style: normal;
        }
      }

      // /* 滚动条整体部分 */
      &::-webkit-scrollbar {
        height: 6px; /* 滚动条高度 */
        width: 6px; /* 滚动条宽度 */
      }
      // /* 滚动条滑块 */
      &::-webkit-scrollbar-thumb {
        background-color: transparent; /* 滑块颜色 */
        border-radius: 10px;
      }
      &:hover::-webkit-scrollbar-thumb {
        background-color: #888; /* 滑块颜色 */
      }
    }

    .chat-box {
      width: 60%; /* 占满页面宽度 */
      box-sizing: border-box; /* 包括内边距和边框 */
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      height: auto;
      background: #f2f8ff;
      border-radius: 12px;
      margin-top: 24px;

      .tip-box {
        width: 100%;
        height: 44px;
        padding: 10px 20px;
        display: flex;
        align-items: center;
        .tip-icon {
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
        }
        .title {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #234772;
          line-height: 20px;
          text-align: right;
          font-style: normal;
          padding: 1px 2px 0 7px;
        }
        .divider {
          width: 1px;
          height: 21px;
          margin: 0 10px;
          color: #c9dfff;
          border-radius: 1px;
        }
        .text {
          // width: 331px;
          height: 24px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: rgba(35, 71, 114, 0.8);
          line-height: 24px;
          text-align: left;
          font-style: normal;
        }
      }

      .input-box {
        position: relative;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        min-height: 56px;
        background: #ffffff;
        box-shadow: 0px 4px 8px 0px rgba(184, 196, 213, 0.2);
        // border-radius: 12px;
        border-bottom-right-radius: 12px;
        border-bottom-left-radius: 12px;
        border: 1px solid #e9edf2;
        padding: 16px;
        > textarea {
          border: none !important;
          border-radius: 0 !important;
        }
        :deep(.custom-textarea) {
          position: relative;
          box-sizing: border-box;
          font-family: PingFangSC, 'PingFang SC';
          font-size: 15px;
          font-weight: 400;
          color: #fff;
          padding: 0;
          background: rgb(255 255 255 / 20%);
          resize: none; /* 禁止用户调整大小 */

          /* 隐藏滚动条 */
          // &::-webkit-scrollbar {
          //   display: none;
          // }

          /* 占位符样式 */
          &::placeholder {
            color: #cccccc;
          }
          &:hover,
          &:focus-within {
            border: none !important;
            box-shadow: none !important;
          }
        }
        .send-button {
          width: 32px;
          height: 32px;
          cursor: pointer;
        }
      }

      border-bottom-right-radius: 12px;
      border-bottom-left-radius: 12px;
    }
    .loader {
      width: 24px;
      aspect-ratio: 1;
      border-radius: 50%;
      background:
        radial-gradient(farthest-side, #1777ff 94%, #0000) top/3px 3px no-repeat,
        conic-gradient(#0000 30%, #1777ff);
      -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 3px), #000 0);
      animation: l13 1s infinite linear;
    }
    @keyframes l13 {
      100% {
        transform: rotate(1turn);
      }
    }

    .model-nmae {
      position: absolute;
      background: #ffffff;
      box-shadow:
        0px 9px 28px 8px rgba(0, 0, 0, 0.05),
        0px 6px 16px 0px rgba(0, 0, 0, 0.08),
        0px 3px 6px -4px rgba(0, 0, 0, 0.12);
      border-radius: 2px;
      padding: 9px 12px;
      display: flex;
      justify-content: center;
      top: 21px;
      left: 8px;
      > span:nth-child(1) {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        text-align: left;
        font-style: normal;
        margin-right: 8px;
      }
    }
  }
</style>
