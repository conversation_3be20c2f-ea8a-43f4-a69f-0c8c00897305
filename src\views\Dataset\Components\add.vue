<script setup lang="ts">
  import { uploadImages } from '@/api/exploration';
  import { message, type UploadProps } from 'ant-design-vue';
  import { DownloadOutlined } from '@ant-design/icons-vue';
  import type { Rule } from 'ant-design-vue/es/form';
  import { ref, reactive } from 'vue';
  import { createDataset } from '@/api/dataset';
  import type { ICreateDataset } from '@/interface/dateset';
  import { datasetTypes, dataSourceOptions } from '.';
  import { useRouter } from 'vue-router';
  const router = useRouter();
  const formState = reactive({
    name: '',
    summary: '',
    source: 'self-developed',
    type: 'sft',
  });
  const fileList = ref<UploadProps[]>([]);
  const files = ref();
  const loading = ref(false);
  const confirmLoading = ref(false);
  const formRef = ref();
  const templates: Record<string, { xlsx: string; json: string }> = {
    sft: {
      xlsx: 'https://minio-prod.shukeyun.com/deepfox/a15158a075d6a17f305bbe49ba0521e7.xlsx',
      json: 'https://minio-prod.shukeyun.com/deepfox/66fa722da2ce2192ad15bb7d66d8b7c9.json',
    },
    'model-distillation': {
      xlsx: 'https://minio-prod.shukeyun.com/deepfox/5885a0b29bea6c1ce84a0f62a8dcacf5.xlsx',
      json: 'https://minio-prod.shukeyun.com/deepfox/11b3d79149267486a4bc42a093f8768e.json',
    },
    'legacy-ml': {
      xlsx: 'https://minio-prod.shukeyun.com/deepfox/6e1506d3c4939805c753bbf1239c7d84.xlsx',
      json: 'https://minio-prod.shukeyun.com/deepfox/d0169db10d94ba26c6217f233e9f0711.json',
    },
  };

  const validatorName = (_rule: Rule, value: string) => {
    if (value == '' || value.trim().length == 0) {
      return Promise.reject('请输入数据集名称');
    }
    const reg = /^[a-zA-Z0-9][a-zA-Z0-9_-]*$/;
    if (!reg.test(value)) {
      return Promise.reject(
        '模型名称仅支持 英文（大小写）、数字、中划线 -、下划线 _，且不能以下划线 _ 或中划线 - 开头',
      );
    }
    if (value.length < 2 || value.length > 64) {
      return Promise.reject('模型名称请输入2-64个字');
    }
    return Promise.resolve();
  };

  const validatorFiles = () => {
    if (!fileList.value.length) {
      return Promise.reject('请上传文件');
    }
    return Promise.resolve();
  };
  const handleDownload = (type: string) => {
    const href = type === 'xlsx' ? templates[formState.type].xlsx : templates[formState.type].json;
    window.open(href);
  };

  // @ts-expect-error
  const beforeUpload = (file: UploadProps['fileList'][number]) => {
    const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
    // 检查文件扩展名是否有效
    if (!['.xlsx', '.xls', '.json'].includes(fileExtension)) {
      message.error(`不支持的文件格式：${file.name}`);
      return false;
    }
    // 检查文件大小
    const isWithinSizeLimit = file.size / 1024 / 1024 <= 200;
    if (!isWithinSizeLimit) {
      message.error(`上传的文件太大，最大不能超过200MB！`);
      return false;
    }
    return true;
  };

  const customRequest = async (options: { file: File; onSuccess: () => void; onError: () => void }) => {
    const { file, onSuccess, onError } = options;
    files.value = file;
    const formData = new FormData();
    formData.append('files', file);
    try {
      await uploadImages(formData);
      onSuccess();
    } catch {
      onError();
    }
  };

  const confirmAdd = async () => {
    confirmLoading.value = true
    await formRef.value.validateFields();
    const params: ICreateDataset = {
      ...formState,
      file: files.value,
    };
    await createDataset(params);
    confirmLoading.value = false
    router.push('/dataset');
  };
</script>

<template>
  <div class="flex flex-col h-100%">
    <div class="content">
      <a-form
        ref="formRef"
        autocomplete="off"
        layout="vertical"
        :model="formState"
        v-bind="{
          labelCol: { span: 2 },
          wrapperCol: { span: 15 },
        }"
      >
        <a-form-item label="数据集名称" name="name" :rules="[{ required: true, validator: validatorName }]">
          <a-input
            v-model:value="formState.name"
            style="width: 100%"
            show-count
            :maxlength="50"
            placeholder="请输入数据集名称"
          />
        </a-form-item>
        <a-form-item label="简介" name="summary">
          <a-textarea
            v-model:value="formState.summary"
            allow-clear
            show-count
            :maxlength="500"
            :auto-size="{ minRows: 2, maxRows: 6 }"
            placeholder="请输入数据集简介"
          />
        </a-form-item>
        <a-form-item label="数据来源" name="source" :rules="[{ required: true }]">
          <a-select v-model:value="formState.source" placeholder="请选择数据来源" style="width: 300px">
            <a-select-option v-for="opt in dataSourceOptions" :key="opt.value" :value="opt.value">{{
              opt.label
            }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="数据集类型" name="type" :rules="[{ required: true }]">
          <div class="types">
            <template v-for="item in datasetTypes" :key="item.value">
              <div
                class="types-items"
                :class="{ active: formState.type === item.value }"
                @click="formState.type = item.value"
              >
                <div class="flex-col">
                  <div class="font-bold">{{ item.label }}</div>
                  <div class="c-#7f7f7f font-size-14px">{{ item.desc }}</div>
                </div>
                <div v-if="formState.type === item.value" class="checkbox">
                  <a-checkbox :checked="true"></a-checkbox>
                </div>
              </div>
            </template>
          </div>
        </a-form-item>
        <a-form-item label="选择文件" name="fileList" :rules="[{ required: true, validator: validatorFiles }]">
          <a-upload-dragger
            v-model:file-list="fileList"
            name="file"
            :max-count="1"
            accept=".xlsx,.xls,.json"
            :before-upload="beforeUpload"
            :custom-request="customRequest"
          >
            <loading-outlined v-if="loading"></loading-outlined>
            <plus-outlined v-else></plus-outlined>
            <div class="ant-upload-text">将文件拖放此处，或点击上传</div>
            <div class="ant-upload-text text-#797979 p-x-10px">
              支持 .xlsx/.xls/.json三种文件格式；单个文件大小不超过 200MB。
            </div>
          </a-upload-dragger>
        </a-form-item>
        <a-button type="link" @click="handleDownload('xlsx')"><DownloadOutlined />EXCEL数据模版</a-button>
        <a-button type="link" @click="handleDownload('json')"><DownloadOutlined />JSON数据模版</a-button>
      </a-form>
    </div>
    <a-row>
      <a-col :span="15">
        <div class="footer">
          <a-button type="primary" style="margin-right: 10px" :loading="confirmLoading" @click="confirmAdd"
            >确定</a-button
          >
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<style scoped lang="less">
  .types {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px;
    cursor: pointer;
    border-radius: 2px;

    .types-items {
      position: relative;
      display: flex;
      flex: 1 1 32%;
      max-width: 32%;
      padding: 10px;
      background-color: #fff;
      border: 1px solid #ccc;
    }

    .checkbox {
      position: absolute;
      top: 2px;
      right: 4px;
      width: 16px;
      height: 16px;
    }

    .active {
      border: 1px solid #1677ff;
    }
  }
  .content {
    flex: 1;
  }
  .footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    height: 40px;
  }
</style>
