<script setup lang="ts">
  import { ref, reactive, onMounted, nextTick, watch, onUnmounted } from 'vue';
  import { CustomForm } from '@/components';
  import type { IFormItem, IPagination } from '@/interface';
  import { TABLE_PAGINATION } from '@/json/common';
  import { TrainStatus, STATE_MAP, DATASET_MAP, DATASET_TYPE_MAP } from '.';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table/interface';
  import { useRoute, useRouter } from 'vue-router';
  import { deleteTrainTask, getTaskList, taskOperation } from '@/api/model';
  import { message } from 'ant-design-vue';
  import { formatTime, convertIsoTimeToLocalTime } from '@/utils/common';
  import { GPUList } from '@/views/Model/Train/index';

  const route = useRoute();
  const router = useRouter();
  const DEFAULT_SEARCHSTATE = {
    task_name: undefined,
    image_name: undefined,
    status: undefined,
  };
  const searchState = reactive({ ...DEFAULT_SEARCHSTATE });
  const formConfig: IFormItem[] = [
    {
      field: 'task_name',
      type: 'input',
      label: '任务名称',
      placeholder: '请输入',
    },
    {
      field: 'model_name',
      type: 'input',
      label: '预训练模型',
      placeholder: '请输入',
    },
    {
      field: 'status',
      type: 'select',
      label: '状态',
      placeholder: '请选择',
      options: TrainStatus,
    },
  ];
  const columns: ColumnType[] = [
    { title: '任务名称', dataIndex: 'task_name', fixed: 'left' },
    { title: '显卡信息', dataIndex: 'gpu_number_list', width: 120 },
    { title: '预训练模型', dataIndex: 'model_name' },
    { title: '训练方式', dataIndex: 'method_parameter' },
    { title: '训练数据集', dataIndex: 'dataset' },
    { title: '输出模型名称', dataIndex: 'output_name' },
    { title: '状态', dataIndex: 'status' },
    { title: '创建时间', dataIndex: 'created_at' },
    { title: '更新时间', dataIndex: 'updated_at' },
    { title: '运行时长', dataIndex: 'run_time' },
    { title: '操作', dataIndex: 'operation', width: 240, fixed: 'right' },
  ];
  const pagination = reactive({ ...TABLE_PAGINATION });
  const page: IPagination = reactive({ page_index: 1, page_size: 10 });
  const loading = ref(false);
  const dataSource: Record<string, string>[] = reactive([]);
  const timer = ref();
  const tableHeight = ref(0);
  const stopLoad = ref(false);
  const statusState = reactive<{ list: boolean }>({ list: false });

  const getTaskListReq = async () => {
    loading.value = true;
    const data = await getTaskList({
      ...{ page: page.page_index, limit: page.page_size },
      ...searchState,
    });
    const { total, items: list } = data;
    dataSource.length = 0;
    dataSource.push(...list);
    Object.assign(pagination, { current: page.page_index, total: total });
    if (!data.finished) {
      if (timer.value) {
        clearInterval(timer.value);
        timer.value = null;
      }
      startTaskListRR();
    }
    Object.assign(statusState, { list: data.finished });
    loading.value = false;
  };
  const getTableHeight = () => {
    const tableItem = document.querySelector('.container');
    tableHeight.value = tableItem?.clientHeight as number;
  };

  const startTaskListRR = () => {
    timer.value = setInterval(async () => {
      if (statusState.list) {
        clearInterval(timer.value);
        timer.value = null;
        return;
      }
      const data = await getTaskList({
        ...{ page: page.page_index, limit: page.page_size },
        ...searchState,
      });
      const { total, items: list } = data;
      dataSource.length = 0;
      dataSource.push(...list);
      Object.assign(pagination, { current: page.page_index, total: total });
      Object.assign(statusState, { list: data.finished });
    }, 10000);
  };
  const jumpToDetail = (record: { model_id: string }) => {
    const { model_id } = record;
    router.push({
      path: '/model/detail',
      query: { ...route.query, modelid: model_id },
    });
  };
  const taskInfo = (id: string, type?: string) => {
    router.push({
      path: '/fine-tuned/detail',
      query: { ...route.query, taskid: id, type, category: 'trained' },
    });
  };
  const toggleTable = (_pagination: TablePaginationConfig) => {
    let { current, pageSize } = _pagination;
    console.log(current, pageSize);
    Object.assign(pagination, { current, pageSize });
    Object.assign(page, { page_index: current, page_size: pageSize });
    getTaskListReq();
  };
  const deleteTaskItem = async (id: string) => {
    // await deleteTask(id);
    await deleteTrainTask(id);
    message.success('删除成功');
    getTaskListReq();
  };
  const handleStopTask = async (id: string) => {
    // await stopTask(id);
    stopLoad.value = true;
    await taskOperation({ opration: 'stop', oid: id });
    message.success('停止成功');
    stopLoad.value = false;
    getTaskListReq();
  };
  const onFinish = (values: Record<string, string>) => {
    const searchConfig: { [key: string]: string } = {};
    for (let key in values) {
      searchConfig[key] = values[key];
    }
    Object.assign(searchState, { ...searchConfig });
    page.page_index = 1;
    getTaskListReq();
  };
  const onRest = () => {
    Object.assign(pagination, { ...TABLE_PAGINATION });
    Object.assign(page, { page_index: 1, page_size: 10 });
    Object.assign(searchState, { ...DEFAULT_SEARCHSTATE });
    getTaskListReq();
  };

  onMounted(async () => {
    await nextTick();
    getTableHeight();
  });

  onUnmounted(() => {
    clearInterval(timer.value);
    timer.value = null;
  });
  watch(
    () => route.path,
    (path) => {
      if (path === '/fine-tuned') {
        getTaskListReq();
      } else {
        clearInterval(timer.value);
        timer.value = null;
      }
    },
    { deep: true, immediate: true },
  );
</script>

<template>
  <template v-if="route.name === 'task_detail'">
    <router-view></router-view>
  </template>
  <template v-else>
    <CustomForm :form-items="formConfig" @on-finish="onFinish" @on-rest="onRest" />
    <a-table
      :data-source="dataSource"
      :columns="columns"
      :pagination="pagination"
      :loading="loading"
      :scroll="{ y: tableHeight - 200 }"
      @change="toggleTable"
    >
      <template #bodyCell="{ column, record, text }">
        <div v-if="column.dataIndex === 'operation'" class="operation-box">
          <a @click="taskInfo(record.id)">查看</a>
          <a-button
            v-if="record.status === STATE_MAP.RUNNING"
            :loading="stopLoad"
            type="link"
            style="padding: 0; margin-right: 10px"
            @click="handleStopTask(record.id)"
            >停止</a-button
          >
          <a v-if="record.status === STATE_MAP.COMPLETED" @click="taskInfo(record.id, 'deploy')">部署</a>
          <a-popconfirm
            v-if="![STATE_MAP.RUNNING].includes(record.status)"
            title="确定删除这条任务吗?"
            @confirm="deleteTaskItem(record.id)"
          >
            <a class="del-btn">删除</a>
          </a-popconfirm>
        </div>
        <div v-else-if="column.dataIndex === 'dataset'">
          <template v-if="record.dataset_parameter">
            <div>
              {{ DATASET_TYPE_MAP[record.dataset_parameter.type] }}
              {{ `（${DATASET_MAP[record.dataset_parameter.name] || record.dataset_parameter.name}）` }}
            </div>
          </template>
          <template v-else>--</template>
        </div>
        <div v-else-if="['created_at', 'updated_at'].includes(column.dataIndex)">
          {{ convertIsoTimeToLocalTime(text) }}
        </div>
        <div v-else-if="column.dataIndex === 'gpu_number_list'">
          {{
            text
              ? GPUList.filter((item) => text.includes(item.value as number))
                  .map((i) => i.label)
                  .join(',')
              : '--'
          }}
        </div>
        <div v-else-if="column.dataIndex === 'model_name'">
          <a class="table-a-btn" @click="jumpToDetail(record)">{{ record.model_name }}</a>
        </div>
        <div v-else-if="column.dataIndex === 'run_time'">
          {{ formatTime(text) }}
        </div>
        <div v-else-if="column.dataIndex === 'method_parameter'">
          <template v-if="record.tf_parameter">
            {{ `${record.tf_parameter.type} / ${record.tf_parameter.stage} / ${record.tf_parameter.finetuning_type}` }}
          </template>
          <template v-else>--</template>
        </div>
        <div v-else-if="column.dataIndex === 'status'">
          <a-tag :color="TrainStatus.find((item) => item.value === text)?.color">{{
            TrainStatus.find((item) => item.value === text)?.label
          }}</a-tag>
        </div>
        <div v-else v-ellipse-tooltip.right>{{ text }}</div>
      </template>
    </a-table>
  </template>
</template>

<style scoped lang="less">
  :deep(.ant-table-cell-ellipsis div) {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
