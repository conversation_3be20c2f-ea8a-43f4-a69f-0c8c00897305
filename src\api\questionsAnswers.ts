import request from '@/utils/request';
import { getEnv } from '@/utils';

// 定义宽松的对象类型
type TLooseObject = Record<string, unknown>;

const { VITE_APP_AVATAR_URL } = getEnv();

const AVATAR = VITE_APP_AVATAR_URL;

export function historyId(params?: TLooseObject) {
  return request.$Axios.post(`intelligent-customer-service/jianmen_pass/init[${AVATAR}]`, params);
}

export function chat(params?: any) {
  return request.$Axios.get(`intelligent-customer-service/jianmen_pass/chat[${AVATAR}]`, params);
}

export function TTS(params?: any) {
  return request.$Axios.post(`intelligent-customer-service/tts/[${AVATAR}]`, params);
}

// https://dev-gcluster.shukeyun.com/algorithm/intelligent-customer-service/tts/

export function recommandation(params?: any) {
  return request.$Axios.post(`intelligent-customer-service/jianmen_pass/question_recommandation[${AVATAR}]`, params);
}

export function fetchHistory(user_id: string) {
  return request.$Axios.get(`/intelligent-customer-service/jianmen_pass/histories/${user_id}[${AVATAR}]`);
}

export function deleteHistory(history_id: string) {
  return request.$Axios.del(`/intelligent-customer-service/jianmen_pass/histories/${history_id}[${AVATAR}]`);
}


