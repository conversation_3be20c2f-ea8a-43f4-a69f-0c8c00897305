export const datasetTypes = [
  {
    label: '文本生成-SFT',
    value: 'sft',
    desc: '基于 Self - Instruct 方法构建，由一系列 “Instruction+（可选 Input）+Output” 组成的训练数据。',
  },
  {
    label: '模型蒸馏/评测',
    value: 'model-distillation',
    desc: '仅包含 Prompt 的数据，可用于模型蒸馏与评测，助力训练及验证模型效果。',
  },
  {
    label: '传统机器学习训练',
    value: 'legacy-ml',
    desc: '包含特征与标签的结构化数据，需满足完整性与一致性，支撑传统机器学习模型的训练和评估。',
  },
];
export const dataSourceOptions = [
  {
    label: '自研数据',
    value: 'self-developed',
  },
  {
    label: '第三方',
    value: 'third-party',
  },
  {
    label: '业务数据',
    value: 'business-data',
  },
];
export const DATASET_IMPORT_STATE_MAP = {
  IMPORTING: 'importing',
  IMPORTED: 'imported',
  FAILED: 'failed'
}
export const import_status = [
  { label: '成功', value: 'imported', color: 'green' },
  { label: '导入中', value: 'importing', color: 'orange' },
  { label: '失败', value: 'failed', color: 'red' },
]
export const DATASET_PUBLISH_STATE_MAP = {
  PUBLISHED: 'published',
  UNPUBLISHED: 'unpublished',
  PUBLISHING: 'publishing',
  FAILED: 'failed'
}
export const publish_status = [
  { label: '成功', value: 'published', color: 'green' },
  { label: '未发布', value: 'unpublished', color: 'orange' },
  { label: '发布中', value: 'publishing', color: 'orange' },
  { label: '失败', value: 'failed', color: 'red' },
]

export const exportFormatOptions = [
  { label: 'json', value: 'json' },
  { label: 'xlsx', value: 'xlsx' },
  { label: 'csv', value: 'csv' },
];

/**
 * 将字节转换为最适合的人类可读格式
 * @param bytes 字节大小
 * @param precision 保留小数位数 (默认2位)
 * @returns 转换后的带单位字符串 (如 "1.23 GB")
 */
export function formatBytes(bytes: number, precision: number = 2): string {
  // 参数验证
  if (typeof bytes !== 'number' || isNaN(bytes)) {
    throw new Error('输入必须是有效数字');
  }
  // 处理特殊值
  if (bytes === 0) return '0 Bytes';
  // 处理负值
  const isNegative = bytes < 0;
  if (isNegative) bytes = Math.abs(bytes);
  // 单位定义 (从字节到PB)
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const unitThreshold = 1024;
  // 计算最适合的单位
  let unitIndex = 0;
  while (bytes >= unitThreshold && unitIndex < units.length - 1) {
    bytes /= unitThreshold;
    unitIndex++;
  }
  // 确保精度在合理范围内 (0-8)
  const validPrecision = Math.min(Math.max(0, precision), 8);
  // 处理整数单位不需要小数的情况
  const shouldShowDecimal = bytes % 1 !== 0 && validPrecision > 0;
  const formattedValue = shouldShowDecimal
    ? bytes.toFixed(validPrecision)
    : Math.round(bytes).toString();
  // 添加负号前缀并返回结果
  return `${isNegative ? '-' : ''}${formattedValue} ${units[unitIndex]}`;
}
