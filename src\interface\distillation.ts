import type { IPage } from ".";

export interface IFetchDistillationList extends IPage {
  name?: string;
  status?: string;
}

export interface IDistillationItems {
  name: string;
  teacher_model_id: string;
  student_model_id: string;
  finetuning_type: string;
  task_type: string;
  output_name: string;
  output_model_id: null;
  dataset_parameter: Record<string, string | number>;
  train_parameter: Record<string, string | number>;
  gpu_number_list: number[];
  docker_image: string;
  max_run_time: number;
  run_time: number;
  status: string;
  id: string;
  creator_id: string;
  updater_id: string;
  created_at: string;
  updated_at: string;
  deleted_at: null;
}


export interface ICreateDistillationTask {
  name: string;
  docker_image_id: string;
  teacher_model_id: string;
  student_model_id: string;
  output_name: string;
  gpu_number_list: number[];
  dataset_parameter: Record<string, unknown> | null;
  train_parameter: Record<string, string | number>;
  max_run_time: number;
  finetuning_type?: string;
  task_type?: string;
}