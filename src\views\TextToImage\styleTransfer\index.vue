<script lang="ts" setup>
  import { onBeforeMount, reactive, ref } from 'vue';
  import {
    PlusOutlined,
    InfoCircleOutlined,
    UploadOutlined,
    LoadingOutlined,
    SyncOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import EmptyImage from '@/assets/image/base/pictures/emptyImage.png';
  import ChangeStyleFail from '@/assets/image/base/pictures/changeStyleFail.png';
  import type { UploadProps } from 'ant-design-vue';
  import { getModelList, upload } from '@/api/textToImage';

  import { h } from 'vue';
  const indicator = h(LoadingOutlined, {
    style: {
      fontSize: '24px',
    },
    spin: true,
  });

  const formItemLayout = {
    labelCol: { span: 14 },
    wrapperCol: { span: 22 },
  };

  const themeImageError = ref(false);
  const styleImageError = ref(false);
  const promptError = ref(false);

  const uploadThemeImageUrl = ref('');
  const uploadStyleImageUrl = ref('');
  const isThemeImageUploaded = ref(true);
  const isStyleImageUploaded = ref(true);
  const loading = ref(false);
  const styleUrlLoading = ref(false);

  const resultImageUrl = ref('');
  const resultLoading = ref(false);
  const resultState = ref('init'); // init：未触发合成的初始态 loading：合成中 success：合成成功 fail：合成失败

  const modelList = ref<any>([]);
  const activeModel = ref<any>({ name: 'black-forest-labs/FLUX.1-dev' });

  const formState = reactive<Record<string, any>>({
    theme_image: [],
    style_image: [],
    negative_prompt: '',
    prompt: '',
  });

  const uploadThemeImageProps = {
    beforeUpload: (file: File) => {
      const is = ['image/png', 'image/jpg', 'image/jpeg'].includes(file.type);
      if (!is) {
        message.error('请上传jpg、jpeg、png格式图片');
      }
      return is;
    },
    customRequest: async (detail: { file: File }) => {
      const file = detail.file;
      const formData = new FormData();
      formData.append('face', file);
      loading.value = true;
      upload(formData, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      })
        .then((data: { [key: string]: string }) => {
          uploadThemeImageUrl.value = data?.[0];
          isThemeImageUploaded.value = data?.[0] ? true : false;
        })
        .catch(() => {
          message.error('上传失败');
        })
        .finally(() => {
          loading.value = false;
        });
    },
    multiple: false,
    fileList: [],
    accept: 'image/png,image/jpg,image/jpeg',
    showUploadList: false,
  };

  const uploadStyleImageProps = {
    beforeUpload: (file: File) => {
      const is = ['image/png', 'image/jpg', 'image/jpeg'].includes(file.type);
      if (!is) {
        message.error('请上传jpg、jpeg、png格式图片');
      }
      return is;
    },
    customRequest: async (detail: { file: File }) => {
      const file = detail.file;
      const formData = new FormData();
      formData.append('face', file);
      styleUrlLoading.value = true;
      upload(formData, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      })
        .then((data: { [key: string]: string }) => {
          uploadStyleImageUrl.value = data?.[0];
          isStyleImageUploaded.value = data?.[0] ? true : false;
        })
        .catch(() => {
          message.error('上传失败');
        })
        .finally(() => {
          styleUrlLoading.value = false;
        });
    },
    multiple: false,
    fileList: [],
    accept: 'image/png,image/jpg,image/jpeg',
    showUploadList: false,
  };

  const submitFormData = async () => {
    // 校验
    themeImageError.value = !uploadThemeImageUrl.value;
    styleImageError.value = !uploadStyleImageUrl.value;
    promptError.value = !formState.prompt || !formState.prompt.trim();

    if (themeImageError.value || styleImageError.value || promptError.value) {
      message.error('请完善所有必填项');
      return;
    }
    try {
      resultLoading.value = true;
      resultState.value = 'loading'; // 合成中
      const formData = new FormData();
      formData.append('negative_prompt', formState.negative_prompt || '');
      formData.append('prompt', formState.prompt || '');

      if (formState.theme_image.length > 0) {
        const themeFile = formState.theme_image[0].originFileObj;
        formData.append('theme_image', themeFile);
      }

      if (formState.style_image.length > 0) {
        const styleFile = formState.style_image[0].originFileObj;
        formData.append('style_image', styleFile);
      }

      const response = await fetch(
        'https://dev-gcluster.shukeyun.com/algorithm/image2image/image_workflow/combine_flux',
        {
          method: 'POST',
          body: formData,
        },
      );

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200) {
          if (result.data) {
            resultImageUrl.value = result.data[0];
          }
          resultState.value = 'success'; // 合成成功
          resultLoading.value = false;
          message.success('图片已生成');
        } else if (result.code === 400) {
          resultLoading.value = false;
          resultState.value = 'fail'; // 合成失败
          message.error('图片合成失败，请重试！');
        } else {
          resultLoading.value = false;
        }
      } else {
        resultLoading.value = false;
        console.error('接口调用失败22222222:', response.statusText);
        message.error('图片合成失败，请重试！');
      }
    } catch (error) {
      resultLoading.value = false;
      resultState.value = 'init'; // 恢复初始
      console.error('接口调用异常:', error);
      message.error('图片合成异常，请检查网络或稍后重试！');
    }
  };

  const reset = () => {
    formState.theme_image = [];
    formState.style_image = [];
    formState.negative_prompt = '';
    formState.prompt = '';
    uploadThemeImageUrl.value = '';
    uploadStyleImageUrl.value = '';
    isThemeImageUploaded.value = false;
    isStyleImageUploaded.value = false;
    styleImageError.value = false;
    themeImageError.value = false;
    promptError.value = false;
  };

  const handleThemeImageChange = async (info: UploadProps) => {
    // const { file } = info;
    console.log('themeFile:', info);
    themeImageError.value = false;
    formState.theme_image = info.fileList;
  };

  const handleStyleImageChange = async (info: UploadProps) => {
    // const { file } = info;
    console.log('Stylefile:', info);
    styleImageError.value = false;
    formState.style_image = info.fileList;
  };

  onBeforeMount(async () => {
    const models = await getModelList();
    modelList.value = models?.['image-text-to-image'];
    activeModel.value = modelList.value[0];
  });
</script>
<template>
  <div class="style-transfer-container">
    <div class="form-container">
      <a-form :model="formState" name="validate_other" v-bind="formItemLayout">
        <div class="form-content">
          <a-form-item>
            <template #label>
              <span class="text" style="margin-right: 5px">1. 模型</span>
            </template>
            <a-select v-model:value="activeModel.name" :disabled="true">
              <template #suffixIcon>
                <a-tooltip placement="bottom" color="#fff" :overlay-inner-style="{ width: '320px' }">
                  <template v-if="activeModel" #title><slot name="modelInfo" :value="activeModel"></slot></template>
                  <InfoCircleOutlined
                    class="info-icon"
                    style="color: rgba(0, 0, 0, 0.35); cursor: pointer; font-size: 14px"
                  />
                </a-tooltip>
              </template>
              <a-select-option v-for="(n, i) in modelList" :key="i" :value="n.name">{{ n.name }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item name="theme_image" :rules="[{ required: true, message: '请上传主题图片' }]">
            <template #label>
              <span class="text" style="margin-right: 5px">2. 上传主题图片</span>
            </template>
            <div class="custom-upload-avatar">
              <a-upload
                v-bind="uploadThemeImageProps"
                v-model:file-list="formState.theme_image"
                list-type="picture-card"
                :class="['avatar-uploader', { error: themeImageError }]"
                name="files"
                @change="handleThemeImageChange"
              >
                <template #default>
                  <div v-if="uploadThemeImageUrl" class="w-100% h-100px">
                    <div class="upload-preview">
                      <div
                        v-if="uploadThemeImageUrl"
                        :style="{ backgroundImage: `url(${uploadThemeImageUrl})` }"
                        class="avatar-icon"
                      />
                      <div class="upload-overlay">
                        <UploadOutlined class="re-upload-icon" />

                        重新上传
                      </div>
                    </div>
                  </div>
                  <div v-else>
                    <div class="upload-icon-box">
                      <button type="button" class="upload-button">
                        <template v-if="loading">
                          <LoadingOutlined />
                        </template>
                        <template v-else>
                          <PlusOutlined class="upload-plus-icon" />
                          <!-- <p>将图片拖放此处或者点击上传</p> -->
                        </template>
                      </button>
                      <span v-if="!uploadThemeImageUrl" class="error-msg">将图片拖放此处，或点击上传</span>
                    </div>
                  </div>
                </template>
              </a-upload>
              <div v-if="themeImageError" class="upload-error-tip">请上传图片</div>
            </div>
          </a-form-item>

          <a-form-item name="style_image" :rules="[{ required: true, message: '请上传风格图片' }]">
            <template #label>
              <span class="text" style="margin-right: 5px">3. 上传风格图片</span>
            </template>
            <div class="custom-upload-avatar">
              <a-upload
                v-bind="uploadStyleImageProps"
                v-model:file-list="formState.style_image"
                list-type="picture-card"
                :class="['avatar-uploader', { error: styleImageError }]"
                name="files"
                @change="handleStyleImageChange"
              >
                <template #default>
                  <div v-if="uploadStyleImageUrl" class="w-100% h-100px">
                    <div class="upload-preview">
                      <div
                        v-if="uploadStyleImageUrl"
                        :style="{ backgroundImage: `url(${uploadStyleImageUrl})` }"
                        class="avatar-icon"
                      />
                      <div class="upload-overlay">
                        <UploadOutlined class="re-upload-icon" />

                        重新上传
                      </div>
                    </div>
                  </div>
                  <div v-else>
                    <div class="upload-icon-box">
                      <button type="button" class="upload-button">
                        <template v-if="styleUrlLoading">
                          <LoadingOutlined />
                        </template>
                        <template v-else>
                          <PlusOutlined class="upload-plus-icon" />
                          <!-- <p>将图片拖放此处或者点击上传</p> -->
                        </template>
                      </button>
                      <span v-if="!uploadStyleImageUrl" class="error-msg">将图片拖放此处，或点击上传</span>
                    </div>
                  </div>
                </template>
              </a-upload>
              <div v-if="styleImageError" class="upload-error-tip">请上传图片</div>
            </div>
          </a-form-item>

          <span class="text">4. 风格描述提示词</span>
          <a-form-item name="prompt" :rules="[{ required: true, message: '请填入指导风格迁移的正向提示信息' }]">
            <template #label>
              <span class="text2" style="margin-right: 5px">正向提示词</span>

              <a-tooltip placement="right" overlay-class-name="custom-tooltip">
                <template #title> 用于指导风格迁移的正向提示信息 </template>
                <InfoCircleOutlined class="info-icon" />
              </a-tooltip>
            </template>
            <a-textarea
              v-model:value="formState.prompt"
              :rows="5"
              :style="{ resize: 'none' }"
              placeholder="用于指导风格迁移的正向提示信息"
              :class="{ 'input-error': promptError }"
              @input="promptError = false"
            />
            <div v-if="promptError" class="upload-error-tip">请填写正向提示词</div>
          </a-form-item>

          <a-form-item name="negative_prompt">
            <template #label>
              <span class="text2" style="margin-right: 5px">反向提示词</span>
              <a-tooltip placement="right" overlay-class-name="custom-tooltip">
                <template #title> 用于指导风格迁移的负向提示信息 </template>
                <InfoCircleOutlined class="info-icon" />
              </a-tooltip>
            </template>
            <a-textarea
              v-model:value="formState.negative_prompt"
              :rows="5"
              :style="{ resize: 'none' }"
              placeholder="用于指导风格迁移的负向提示信息"
            />
          </a-form-item>
        </div>

        <div class="form-footer">
          <a-button @click="reset">重置</a-button>
          <a-button
            type="primary"
            :disabled="resultLoading"
            :style="resultLoading ? { color: '#fff', background: '#1777FF', opacity: 0.5 } : {}"
            html-type="button"
            @click="submitFormData"
            >立即生成</a-button
          >
        </div>
      </a-form>
    </div>
    <div class="result-container">
      <div class="result-wrapper">
        <div class="result-title">结果图片：</div>
        <div v-if="resultLoading" class="result-mask">
          <div class="result-loading-content">
            <div>
              <SyncOutlined class="sync-icon" spin />
            </div>
            <p>正在生成，请耐心等候...</p>
          </div>
        </div>
        <div v-else class="result-image">
          <img
            v-if="resultImageUrl && resultState === 'success'"
            class="result-success"
            :src="resultImageUrl"
            alt="Result Image"
          />
          <div v-if="resultState === 'fail'" class="result-fail">
            <img class="result-other" :src="ChangeStyleFail" alt="Error Image" />
            <p class="result-text" style="width: 112px; height: 20px">生成失败，请重试</p>
          </div>
          <div v-if="resultState === 'init' && !resultImageUrl" class="result-init">
            <img class="result-other" :src="EmptyImage" alt="Empty Image" />
            <p class="result-text">请根据左侧提示操作，生成的结果图片将显示在此</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
  .style-transfer-container {
    display: flex;
    justify-content: flex-start;
    // padding: 20px;
    // height: calc(100vh - 240px);
    height: 100%;
    // overflow: hidden;

    .text {
      font-weight: 600;
      font-size: 16px;
      color: #17181a;
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
    .text2 {
      font-weight: 600;
      font-size: 14px;
      color: #17181a;
      line-height: 20px;
      text-align: left;
      font-style: normal;
    }
    .form-container {
      width: 28%;
      height: 100%;
      padding-top: 20px;
      border-right: 1px solid #e8e8e8;
      :deep(.ant-form) {
        display: flex;
        flex-direction: column;
        height: 100%;
      }
      .form-content {
        padding-left: 30px;
        // height: 600px;
        flex: 1;
        overflow-y: auto; /* 隐藏滚动条 */
        resize: none; /* 禁止用户调整大小 */

        /* 隐藏滚动条 */
        &::-webkit-scrollbar {
          display: none;
        }
      }

      > span {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #969799;
        line-height: 17px;
        text-align: left;
        font-style: normal;
      }

      :deep(.ant-form-item-row) {
        display: block; /* 将内容分为上下两行 */
        text-align: left; /* 左对齐 */
        line-height: 1.5; /* 调整行高 */
        white-space: normal; /* 允许换行 */
      }

      .form-footer {
        display: flex;
        padding-top: 15px;
        border-top: 1px solid #e8e8e8;
        justify-content: space-around;
        align-items: center;
        button {
          width: calc(50% - 30px);
          height: 44px;
        }
      }
    }

    .result-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      // margin: 40px 200px 70px 200px;
      margin: 0 20px;
      .result-wrapper {
        width: 66%;
        height: 90%;
        .result-title {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 600;
          font-size: 16px;
          color: #17181a;
          line-height: 22px;
          height: 22px;
          text-align: left;
          font-style: normal;
          margin: 10px 0;
        }
        .result-mask {
          color: #fff;
          width: 100%;
          height: calc(100% - 42px);
          background-color: rgba(0, 0, 0, 0.3);
          display: flex;
          justify-content: center;
          align-items: center;
          .result-loading-content {
            color: #fff;
            display: flex;
            flex-direction: column;
            align-items: center;
          }
        }
        .result-image {
          display: flex;
          width: 100%;
          height: calc(100% - 42px);
          justify-content: center;
          flex-direction: column;
          align-items: center;
          background: #f7f8fa;
          border-radius: 8px;
          padding: 10px;
          width: 100%; /* 宽度由父容器决定 */
          aspect-ratio: 1 / 1; /* 强制保持 1:1 的宽高比 */

          .result-init {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
          }

          .result-success {
            width: 100%;
            height: 100%;
            border-radius: 8px;
            object-fit: cover; /* 确保图片适应容器 */
          }

          .result-other {
            height: 100px;
            border-radius: 8px;
            object-fit: cover; /* 确保图片适应容器 */
          }
        }

        .result-text {
          margin-top: 10px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #969799;
          line-height: 24px;
          text-align: right;
          font-style: normal;
        }
      }
    }

    .custom-upload-avatar {
      // padding-bottom: 20px;
      // border-bottom: 1px solid #f0f1f2;
      .avatar-uploader {
        &.error {
          .ant-upload.ant-upload-select,
          .ant-upload.ant-upload-select:hover {
            border: 1px dashed #ff3c16;

            .upload-plus-icon {
              // color: #c8cacc;
            }
          }
        }

        :deep(.ant-upload.ant-upload-select) {
          width: 98%;
          height: 100px;
          border-radius: 8px;
          &:hover .upload-overlay {
            opacity: 1;
          }
        }

        .upload-icon-box {
          display: flex;
          flex-direction: column;

          .upload-button {
            background: #f7f8fa;
            border: 0;
          }
        }

        .upload-preview {
          position: relative;
          width: 100%;
          height: 100%;

          &:hover > .upload-overlay {
            opacity: 1;
          }

          .avatar-icon {
            // width: 384px;
            // height: 96px;
            width: 100%;
            height: 100%;
            background-size: contain; /* 或 cover */
            background-repeat: no-repeat;
            background-position: center;
          }

          .upload-overlay {
            position: absolute;
            top: 0;
            left: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            font-size: 12px;
            font-weight: 400;
            line-height: 17px;
            color: white;
            background: rgb(0 0 0 / 50%);
            opacity: 0;
            transition: opacity 0.3s;

            .re-upload-icon {
              width: 13px;
              height: 12px;
              margin-right: 6px;
            }
          }
        }
      }

      .avatar-format-tip {
        position: relative;
        top: -6px;
        height: 14px;
        font-size: 10px;
        font-weight: 400;
        line-height: 14px;
        color: #636466;
      }
    }

    .avatar-uploader.error :deep(.ant-upload.ant-upload-select),
    .avatar-uploader.error :deep(.ant-upload.ant-upload-select:hover) {
      border: 1px dashed #ff4d4f !important;
    }
    .input-error {
      border: 1px solid #ff4d4f !important;
    }
    .upload-error-tip {
      color: #ff4d4f;
      font-size: 12px;
      margin-top: 4px;
      margin-left: 2px;
    }
  }
</style>
