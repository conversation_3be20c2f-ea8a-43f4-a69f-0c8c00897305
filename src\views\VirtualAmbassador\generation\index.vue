<script lang="ts" setup>
  import { reactive, ref, defineExpose, onBeforeMount } from 'vue';
  import {
    UploadOutlined,
    LoadingOutlined,
    SaveOutlined,
    DownloadOutlined,
    InfoCircleOutlined,
    SyncOutlined,
  } from '@ant-design/icons-vue';
  import { message, Upload } from 'ant-design-vue';
  import EmptyImage from '@/assets/image/base/pictures/emptyImage.png';
  import ChangeStyleFail from '@/assets/image/base/pictures/changeStyleFail.png';
  import type { UploadProps } from 'ant-design-vue';
  import { getLocalItem } from '@/utils/common';
  import { upload, nameValidity, generalVirtualAmbassador, saveVirtualAmbassador } from '@/api/virtualAmbassador';

  import { h } from 'vue';
  import { getModelList } from '@/api/textToImage';
  const indicator = h(LoadingOutlined, {
    style: {
      fontSize: '24px',
    },
    spin: true,
  });

  const formItemLayout = {
    labelCol: { span: 22 },
    wrapperCol: { span: 22 },
  };

  const promptError = ref(false);

  const uploadImage = ref('');
  const isImageUploaded = ref(true);
  const loading = ref(false);
  const iamgeLoading = ref(false);

  const resultImageUrl = ref('');
  const resultLoading = ref(false);
  const resultState = ref('init'); // init：未触发合成的初始态 loading：合成中 success：合成成功 fail：合成失败
  const resultId = ref('');
  const isValidName = ref(true);
  const formState = reactive<Record<string, any>>({
    file: [],
    image: [],
    description: '',
    name: '',
  });

  const personName = ref('');
  const openModal = ref(false);
  // 检查是否有未保存内容
  const hasUnsaved = () => {
    // 只要有填写内容或合成成功但未保存
    return (
      formState.file.length > 0 ||
      formState.image.length > 0 ||
      formState.description ||
      resultState.value === 'loading'
    );
  };

  defineExpose({ hasUnsaved });

  const fileIds = ref<string[]>([]);
  const imageIds = ref<string[]>([]);
  const fileError = ref(false);

  const uploadFileProps = {
    beforeUpload: (file: File) => {
      const isAllowed =
        file.type === 'application/pdf' ||
        file.type === 'application/msword' ||
        file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      if (!isAllowed) {
        message.error('只支持上传doc、docx、pdf格式文件');
      }
      if (file.size > 100 * 1024 * 1024) {
        message.error('文件过大，最大支持100M');
        fileError.value = true; // 触发红色边框
        return Upload.LIST_IGNORE;
      }
      fileError.value = false; // 合法时清除错误态
      return true;
    },
    customRequest: async (detail: { file: File; onSuccess?: Function; onError?: Function }) => {
      const file = detail.file;
      const formData = new FormData();
      formData.append('file', file);
      formData.append('kind', 'doc');
      loading.value = true;
      upload(formData, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      })
        .then((data: { [key: string]: string }) => {
          fileIds.value.push(data?.material_id);

          if (detail.onSuccess) detail.onSuccess(data, file);
        })
        .catch(() => {
          if (detail.onError) detail.onError();
          message.error('上传失败');
        })
        .finally(() => {
          loading.value = false;
        });
    },
    multiple: true,
    fileList: [],
    accept: '.pdf,.doc,.docx',
    showUploadList: false,
  };

  const modelList = ref<any>([]);
  const activeModel = ref<any>({ name: 'black-forest-labs/FLUX.1-dev' });

  const uploadImageProps = {
    beforeUpload: (file: File) => {
      const is = ['image/png', 'image/jpg', 'image/jpeg'].includes(file.type);
      if (!is) {
        message.error('请上传jpg、jpeg、png格式图片');
      }
      return is;
    },
    customRequest: async (detail: { file: File }) => {
      const file = detail.file;
      const formData = new FormData();
      formData.append('file', file);
      formData.append('kind', 'draft');
      iamgeLoading.value = true;
      upload(formData, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      })
        .then((data: { [key: string]: string }) => {
          uploadImage.value = data?.material_url;
          isImageUploaded.value = data?.material_url ? true : false;
          imageIds.value = [];
          if (data?.material_id) {
            imageIds.value.push(data.material_id);
          }
        })
        .catch(() => {
          message.error('上传失败');
        })
        .finally(() => {
          iamgeLoading.value = false;
        });
    },
    multiple: false,
    fileList: [],
    accept: 'image/png,image/jpg,image/jpeg',
    showUploadList: false,
  };

  const checkNameValidity = async () => {
    const name = personName.value?.trim();
    if (!name) return;
    try {
      const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');
      const res = await nameValidity({ image_name: name, user_id: userId });
      if (res.is_valid === true) {
        isValidName.value = true;
      } else {
        isValidName.value = false;
      }
    } catch (e: any) {
      console.error('名称校验异常:', e);
    }
  };

  const submitFormData = async () => {
    // 校验
    promptError.value = !formState.description || !formState.description.trim();

    if (promptError.value) {
      message.error('请填写形象设计描述');
      return;
    }

    try {
      resultLoading.value = true;
      resultState.value = 'loading'; // 合成中
      const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');

      const params = {
        materials: fileIds.value.concat(imageIds.value),
        description: formState.description,
        user_id: userId,
      };

      const result = await generalVirtualAmbassador(params);

      if (result && Object.keys(result).length > 0) {
        resultImageUrl.value = result.image_url;
        resultId.value = result.id;
        resultState.value = 'success'; // 合成成功
        resultLoading.value = false;
        message.success('图片合成成功！');
      } else {
        resultState.value = 'fail'; // 合成失败
        resultLoading.value = false;
      }
    } catch (error) {
      resultLoading.value = false;
      resultState.value = 'fail'; // 恢复初始
      console.error('接口调用异常:', error);
    } finally {
      resultLoading.value = false;
    }
  };

  const saveVirtualAmbassadorList = async () => {
    // 1. 名字为空
    if (!personName.value) {
      message.error('请输入形象名称');
      return;
    }

    // 2. 校验名字是否重复
    await checkNameValidity();
    if (!isValidName.value) {
      message.error('该名称已被占用，请更换');
      return;
    }

    try {
      const res = await saveVirtualAmbassador({ id: resultId.value, name: personName.value });
      if (res && Object.keys(res).length > 0) {
        message.success('保存成功！');
      }
      openModal.value = false;
    } catch (error) {
      console.error('保存异常:', error);
      openModal.value = false;
      // message.error('保存失败，请稍后重试！');
    }
  };

  const downloadImage = async (picture_url: string) => {
    const imageURL = picture_url;
    const response = await fetch(imageURL);
    const blob = await response.blob();
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'downloaded_image.jpg'; // 为下载的图片指定一个文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const reset = () => {
    formState.file = [];
    formState.image = [];
    formState.description = '';
    // formState.name = '';
    uploadImage.value = '';
    isImageUploaded.value = false;
    promptError.value = false;
  };

  const handleFileChange = async (info: UploadProps) => {
    formState.file = info?.fileList?.filter((file) => file.status !== 'error');
  };

  const handleFileRemove = (file: any) => {
    const id = file.response?.material_id || file.material_id;
    if (!id) return;
    const idx = fileIds.value.indexOf(id);
    if (idx > -1) fileIds.value.splice(idx, 1);
  };

  const handleImageChange = async (info: UploadProps) => {
    formState.image = info.fileList;
  };

  onBeforeMount(async () => {
    const models = await getModelList();
    modelList.value = models?.['text-to-image'];
    activeModel.value = modelList.value[0];
  });
</script>
<template>
  <div class="generation-container">
    <div class="form-container">
      <a-form :model="formState" name="validate_other" v-bind="formItemLayout">
        <div class="form-content">
          <a-form-item label="1. 模型" :wrapper-col="{ span: 22 }">
            <a-select v-model:value="activeModel.name" :disabled="true">
              <template #suffixIcon>
                <a-tooltip placement="bottom" color="#fff" :overlay-inner-style="{ width: '320px' }">
                  <template v-if="activeModel" #title><slot name="modelInfo" :value="activeModel"></slot></template>
                  <InfoCircleOutlined
                    class="info-icon"
                    style="color: rgba(0, 0, 0, 0.35); cursor: pointer; font-size: 14px"
                  />
                </a-tooltip>
              </template>
              <a-select-option v-for="(n, i) in modelList" :key="i" :value="n.name">{{ n.name }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="2. 上传材料" name="file" :wrapper-col="{ span: 22 }">
            <div class="custom-upload">
              <span class="custom-upload-desc">
                （如景区宣传文案、历史故事、特色文化等有关文件，便于AI进行特色分析）
              </span>
              <a-tooltip placement="top">
                <template #title>
                  <div>文档格式：支持doc、docx、pdf</div>
                  <div>文档大小：文件最大支持100M</div>
                  <div>文档页数：PDF/Word最多支持1000页</div>
                </template>
                <a-upload
                  v-bind="uploadFileProps"
                  v-model:file-list="formState.file"
                  class="file-uploader"
                  :class="{ error: fileError }"
                  name="files"
                  list-type="picture"
                  :show-upload-list="{
                    showRemoveIcon: true,
                    showDownloadIcon: false,
                    showPreviewIcon: false,
                  }"
                  @change="handleFileChange"
                  @remove="handleFileRemove"
                >
                  <span class="file-upload-button">
                    <span class="plus-icon" style="border: none">+</span>
                    <div style="border: none; font-size: 14px">上传文件</div>
                  </span>
                </a-upload>
              </a-tooltip>
            </div>
          </a-form-item>

          <a-form-item label="3. 上传形象手稿" name="_image">
            <div class="custom-upload">
              <span class="custom-upload-desc">（如有可上传，便于AI以此手稿参考进行生成，支持jpg、jpeg、png格式）</span>
              <a-upload
                v-bind="uploadImageProps"
                v-model:file-list="formState.image"
                list-type="picture-card"
                :class="['avatar-uploader']"
                name="files"
                @change="handleImageChange"
              >
                <template #default>
                  <div v-if="uploadImage">
                    <div class="upload-preview">
                      <div v-if="uploadImage" class="avatar-icon">
                        <img :src="uploadImage" alt="avatar" />
                      </div>
                      <div class="upload-overlay">
                        <UploadOutlined class="re-upload-icon" />

                        重新上传
                      </div>
                    </div>
                  </div>
                  <div v-else>
                    <div class="upload-icon-box">
                      <button type="button" class="upload-button">
                        <template v-if="iamgeLoading">
                          <LoadingOutlined />
                        </template>
                        <template v-else>
                          <span style="font-size: 40px; color: #c8cacc">+</span>
                          <p style="color: #969799; font-size: 14px; margin-top: 10px">将图片拖放此处或者点击上传</p>
                        </template>
                      </button>
                    </div>
                  </div>
                </template>
              </a-upload>
            </div>
          </a-form-item>

          <a-form-item
            label="4. 形象设计描述"
            name="description"
            :rules="[
              { required: true, message: '描述形象的特征、风格，如卡通形象、人物、拟人化动物、性格、外形元素等' },
            ]"
          >
            <a-textarea
              v-model:value="formState.description"
              :auto-size="{ minRows: 4, maxRows: 6 }"
              placeholder="描述形象的特征、风格，如卡通形象、人物、拟人化动物、性格、外形元素等"
              :class="{ 'input-error': promptError }"
              @input="promptError = false"
            />
            <div v-if="promptError" class="upload-error-tip">请填写形象设计描述</div>
          </a-form-item>
        </div>

        <div class="form-footer">
          <a-button class="reset-button" @click="reset">重置</a-button>
          <a-button
            type="primary"
            class="submit-button"
            :disabled="resultLoading"
            :style="resultLoading ? { color: '#fff', background: '#1777FF', opacity: 0.5 } : {}"
            html-type="button"
            @click="submitFormData"
            >立即生成</a-button
          >
        </div>
      </a-form>
    </div>
    <div class="result-container">
      <div class="result-wrapper">
        <div class="result-title">结果图片：</div>
        <div v-if="resultLoading" class="result-mask">
          <div class="result-loading-content">
            <div>
              <SyncOutlined class="sync-icon" spin />
            </div>
            <p>正在生成，请耐心等候...</p>
          </div>
        </div>
        <div v-else class="result-image">
          <img
            v-if="resultImageUrl && resultState === 'success'"
            class="result-success"
            :src="resultImageUrl"
            alt="Result Image"
          />
          <div v-if="resultState === 'fail'" class="result-fail">
            <img class="result-other" :src="ChangeStyleFail" alt="Error Image" />
            <p class="result-text" style="width: 112px; height: 20px">生成失败，请重试</p>
          </div>
          <div v-if="resultState === 'init' && !resultImageUrl" class="result-init">
            <img class="result-other" :src="EmptyImage" alt="Empty Image" />
            <p class="result-text">请根据左侧提示操作，生成的结果图片将显示在此</p>
          </div>
        </div>

        <div v-if="resultState === 'success'" class="result-footer">
          <a-button
            :style="resultLoading ? { color: '#fff', background: '#1777FF', opacity: 0.5 } : {}"
            @click="downloadImage(resultImageUrl)"
          >
            <DownloadOutlined />下载
          </a-button>

          <a-popover v-model:open="openModal" trigger="click" placement="top">
            <template #content>
              <div style="width: 220px">
                <div style="margin-bottom: 8px">保存前，给形象起个名字吧</div>
                <a-input
                  v-model:value="personName"
                  maxlength="10"
                  placeholder="请输入形象名称，10个字内"
                  style="margin-bottom: 8px"
                />
                <div style="text-align: right">
                  <a-button size="small" style="margin-right: 8px" @click="openModal = false">取消</a-button>
                  <a-button size="small" type="primary" @click="saveVirtualAmbassadorList">确定</a-button>
                </div>
              </div>
            </template>
            <a-button :style="resultLoading ? { color: '#fff', background: '#1777FF', opacity: 0.5 } : {}">
              <SaveOutlined />保存
            </a-button>
          </a-popover>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
  .generation-container {
    display: flex;
    justify-content: flex-start;
    // margin: 20px 20px 0px 0px;
    // height: calc(100vh - 240px);
    height: 100%;
    // overflow: hidden;

    .form-container {
      width: 32%;
      height: 100%;
      min-width: 200px;
      border-right: 1px solid #e8e8e8;
      :deep(.ant-form) {
        display: flex;
        flex-direction: column;
        height: 100%;
      }
      .form-content {
        flex: 1;
        padding-left: 30px;
        padding-top: 20px;
        overflow-y: auto; /* 隐藏滚动条 */
        resize: none; /* 禁止用户调整大小 */

        /* 隐藏滚动条 */
        &::-webkit-scrollbar {
          display: none;
        }
        > div {
          margin-bottom: 30px !important;
        }
      }

      > span {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #969799;
        line-height: 17px;
        text-align: left;
        font-style: normal;
      }

      :deep(.ant-form-item-row) {
        display: block; /* 将内容分为上下两行 */
        text-align: left; /* 左对齐 */
        line-height: 1.5; /* 调整行高 */
        white-space: normal; /* 允许换行 */
      }

      :deep(.ant-form-item-label > label) {
        font-size: 16px;
        color: #17181a;
        font-weight: 600;
      }

      .form-footer {
        display: flex;
        padding-top: 15px;
        border-top: 1px solid #e8e8e8;
        justify-content: space-around;
        align-items: center;
        button {
          width: calc(50% - 30px);
          height: 44px;
        }
      }
    }

    .result-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .result-wrapper {
        width: 66%;
        height: 90%;
        .result-title {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 600;
          font-size: 16px;
          color: #17181a;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          margin-bottom: 10px;
        }
        .result-mask {
          color: #fff;
          width: 100%;
          height: calc(100% - 42px);
          background-color: rgba(0, 0, 0, 0.3);
          display: flex;
          justify-content: center;
          align-items: center;
          .result-loading-content {
            color: #fff;
            display: flex;
            flex-direction: column;
            align-items: center;
          }
        }
        .result-image {
          display: flex;
          width: 100%;
          height: calc(100% - 42px);
          justify-content: center;
          flex-direction: column;
          align-items: center;
          background: #f7f8fa;
          border-radius: 8px;
          padding: 10px;
          width: 100%; /* 宽度由父容器决定 */
          aspect-ratio: 1 / 1; /* 强制保持 1:1 的宽高比 */

          .result-init {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
          }

          .result-success {
            width: 100%;
            height: 100%;
            border-radius: 8px;
            object-fit: cover; /* 确保图片适应容器 */
          }

          .result-other {
            height: 100px;
            border-radius: 8px;
            object-fit: cover; /* 确保图片适应容器 */
          }
        }

        .result-text {
          margin-top: 10px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #969799;
          line-height: 24px;
          text-align: right;
          font-style: normal;
        }

        .result-footer {
          display: flex;
          justify-content: flex-end;
          padding-top: 20px;

          > button {
            width: 92px;
            height: 40px;
            background: #ffffff;
            border-radius: 4px;
            border: 1px solid #e6e6e6;
          }

          > button:first-child {
            margin-right: 16px;
          }
        }
      }
    }

    .custom-upload {
      // border-bottom: 1px solid #f0f1f2;
      > span {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 12px;
        line-height: 17px;
        text-align: left;
        font-style: normal;
      }

      .file-uploader {
        width: 100%; // 让它和表单内容对齐
        min-width: 0;
        height: auto;
        border-radius: 8px;
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        justify-content: flex-start;
        margin-top: 12px;

        .file-upload-button {
          background: #f7f8fa;
          font-size: 14px;
          width: 100%;
          height: 60px;
          display: flex;
          color: #969799;
          border: 1px dashed #e6e6e6; // 虚线边框，主色
          border-radius: 8px;
          justify-content: center;
          align-items: center;

          .plus-icon {
            font-size: 20px;
            margin-top: 4px;
            width: 20px;
            height: 20px;
            color: #969799;
          }
        }

        :deep(.ant-upload) {
          width: 100%;
          height: 100%;
          border-radius: 8px;
          border: none; // 避免双重边框
          :hover {
            border: 1px dashed #1777ff; // 虚线边框，主色
          }
        }
      }

      .custom-upload-desc {
        color: #969799;
      }

      .file-format-tip {
        color: #1777ff;
      }
      .avatar-uploader {
        position: relative;
        &.error {
          .ant-upload.ant-upload-select,
          .ant-upload.ant-upload-select:hover {
            border: 1px dashed #ff3c16;
          }
        }

        :deep(.ant-upload.ant-upload-select) {
          width: 148px;
          height: 148px;
          border-radius: 8px;
          margin-top: 12px;
          position: relative;
        }

        .upload-icon-box {
          display: flex;
          flex-direction: column;

          .upload-button {
            background: #f7f8fa;
            border: 0;
          }
        }

        .upload-preview {
          // position: relative;
          width: 100%;
          height: 100%;

          &:hover > .upload-overlay {
            opacity: 1;
          }

          .avatar-icon {
            width: 74px;
            height: 96px; /* 需要固定高时加上 */
            background: #fff;
            border-radius: 4px;
            overflow: hidden; /* 防止溢出 */
            display: inline-flex; /* 居中可选 */
            align-items: center;
            justify-content: center;
          }
          .avatar-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain; /* 或 cover 按需选择 */
          }

          .upload-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 400;
            line-height: 17px;
            color: white;
            background: rgb(0 0 0 / 50%);
            opacity: 0;
            transition: opacity 0.3s;

            .re-upload-icon {
              width: 13px;
              height: 12px;
              margin-right: 6px;
            }
          }
        }
      }
    }

    :deep(.ant-upload-list) {
      width: 100%;
    }

    :deep(.ant-input::placeholder),
    :deep(textarea::placeholder) {
      font-size: 12px; // 你想要的字体大小
      color: #bfbfbf; // 可选，设置颜色
    }
    .input-error {
      border: 1px solid #ff4d4f !important;
    }
    .upload-error-tip {
      color: #ff4d4f;
      font-size: 12px;
      margin-top: 4px;
      margin-left: 2px;
    }
  }
</style>
