
import type { IPage } from '@/interface';
import request from '@/utils/request';

export function fetchDockerList(data: IPage) {
  return request.$Axios.get('/docker_image/list', data);
}

export function deleteDocker(id: string) {
  return request.$Axios.del(`/docker_image/${id}`, { id });
}

export function dockerDropDown(use_type?: string) {
  return request.$Axios.get(`/docker_image/filter_list`, { use_type });
}

export function dockerStatus(data: any) {
  return request.$Axios.put(`/docker_image/${data.operation}/${data.oid}`, data);
}