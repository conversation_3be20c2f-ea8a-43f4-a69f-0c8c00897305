<script setup lang="ts">
  import type { IPage } from '@/interface';
  import { TABLE_PAGINATION } from '@/json/common';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table';
  import { ref, reactive, onMounted, onUnmounted } from 'vue';
  import { convertIsoTimeToLocalTime, debounce } from '@/utils/common';
  import { useRouter, useRoute } from 'vue-router';
  import { delateKnowledgeFile, fetchFileList } from '@/api/knowledgebase';
  import type { IKnowledgeFileItem } from '@/interface/knowledge';
  import { message } from 'ant-design-vue';
  import { LeftOutlined } from '@ant-design/icons-vue';
  const router = useRouter();
  const route = useRoute();
  interface IState {
    name?: string;
    status?: string;
  }
  const state = reactive<IState>({
    name: undefined,
    status: undefined,
  });
  const currentRecord = reactive<{ id: string; name: string; description: string }>({
    id: '',
    name: '',
    description: '',
  });
  const visible = reactive({
    delete: false,
    edit: false,
  });
  const tableType: string[] = ['xlsx', 'csv', 'xls'];
  const loading = ref(false);
  const deleteLoading = ref(false);
  const pollingInterval = ref(); // 轮询定时器
  const isAllAvailable = ref(false); // 是否全部可用标志

  const statusOptions: { label: string; value: string; color: string }[] = [
    { label: '处理中', value: 'processing', color: 'orange' },
    { label: '可用', value: 'available', color: 'green' },
    { label: '失败', value: 'failed', color: 'red' },
  ];
  const columns: ColumnType[] = [
    { title: '文件名称', dataIndex: 'name' },
    { title: '文件 ID', dataIndex: 'file_uuid', width: 260 },
    { title: '文件格式', dataIndex: 'type' },
    { title: '数据量', dataIndex: 'size' },
    // { title: '上传人', dataIndex: 'source_path' },
    { title: '上传时间', dataIndex: 'created_at' },
    { title: '状态', dataIndex: 'status' },
    { title: '操作', dataIndex: 'operation', fixed: 'right' },
  ];
  const pagination = reactive({ ...TABLE_PAGINATION });
  const pageParame: IPage = reactive({ page: 1, limit: 10 });
  const tableHeight = ref(0);
  const dataSource = reactive<IKnowledgeFileItem[]>([]);
  const handleExport = () => {
    router.push(`/knowledge/export/${route.params.id}?name=${route.query.name}`);
  };
  const onSearch = () => {
    fetchData();
  };
  const toggleTable = (_pagination: TablePaginationConfig) => {
    const { current, pageSize } = _pagination;
    Object.assign(pagination, { current, pageSize });
    Object.assign(pageParame, { page: current, limit: pageSize });
    fetchData();
  };
  const fetchData = async () => {
    loading.value = true;
    const data: IKnowledgeFileItem[] = await fetchFileList({
      db_id: String(route.params.id),
      ...state,
    });
    dataSource.length = 0;
    dataSource.push(...data);
    checkAllAvailable();
    loading.value = false;
  };
  const debouncedSearch = debounce(fetchData);

  // 检查所有数据是否都变为 'available'
  const checkAllAvailable = () => {
    if (dataSource.length === 0) return false;
    isAllAvailable.value = dataSource.every((item) => item.status === 'available');
    // 如果全部可用，停止轮询
    if (isAllAvailable.value) {
      stopPolling();
    }
    return isAllAvailable.value;
  };

  // 开始轮询
  const startPolling = (interval = 5000) => {
    // 先立即获取一次数据
    fetchData();
    // 设置定时轮询
    pollingInterval.value = setInterval(fetchData, interval);
  };

  // 停止轮询
  const stopPolling = () => {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value);
      pollingInterval.value = null;
    }
  };

  const handleDelete = (record: IKnowledgeFileItem) => {
    const { id, name } = record;
    Object.assign(currentRecord, { id, name });
    visible.delete = true;
  };
  const confirmDelete = async () => {
    deleteLoading.value = true;
    try {
      await delateKnowledgeFile(currentRecord.id);
      message.success(`文件${currentRecord.name}已删除`);
      visible.delete = false;
      // 先停止之前的轮询，再开始新一轮轮询
      stopPolling();
      startPolling();
    } catch {
      deleteLoading.value = false;
    }
  };
  const getTableHeight = () => {
    const tableItem = document.querySelector('.container');
    tableHeight.value = (tableItem?.clientHeight as number) - 52;
  };

  // 组件挂载时开始轮询
  onMounted(() => {
    startPolling();
    getTableHeight();
  });

  // 组件卸载时清除定时器
  onUnmounted(() => {
    stopPolling();
  });
</script>

<template>
  <!-- shadow-[0px_0px_10px_0px_rgba(0,0,0,0.5)] -->

  <div class="header text-18px">
    <LeftOutlined @click="router.back()" />
    <span class="m-l-10px">{{ route.query.name || '--' }}</span>
  </div>
  <div class="flex justify-between m-y-10px">
    <a-button type="primary" @click="handleExport">导入文件</a-button>
    <div>
      <a-input-search
        v-model:value="state.name"
        placeholder="搜索文件名称"
        allow-clear
        style="width: 400px"
        @change="debouncedSearch"
        @blur="onSearch"
      ></a-input-search>
      <a-select
        v-model:value="state.status"
        placeholder="文件状态"
        allow-clear
        style="width: 200px; margin-left: 10px"
        @change="fetchData"
      >
        <a-select-option v-for="opt in statusOptions" :key="opt.value" :value="opt.value">{{
          opt.label
        }}</a-select-option>
      </a-select>
    </div>
  </div>
  <a-table
    :data-source="dataSource"
    :columns="columns"
    :pagination="pagination"
    :scroll="{ y: tableHeight - 150 }"
    @change="toggleTable"
  >
    <template #bodyCell="{ column, record, text }">
      <div v-if="column.dataIndex === 'operation'" class="operation-box">
        <a class="del-btn" @click="handleDelete(record)">删除</a>
      </div>
      <div v-else-if="['created_at', 'updated_at'].includes(column.dataIndex)">
        {{ convertIsoTimeToLocalTime(text) }}
      </div>
      <div v-else-if="column.dataIndex === 'status'">
        <a-tooltip v-if="record.error_msg">
          <template #title>
            {{ record.error_msg }}
          </template>
          <a-tag :color="statusOptions.find((item) => item.value === text)?.color">{{
            statusOptions.find((item) => item.value === text)?.label
          }}</a-tag>
        </a-tooltip>
        <a-tag v-else :color="statusOptions.find((item) => item.value === text)?.color">{{
          statusOptions.find((item) => item.value === text)?.label
        }}</a-tag>
      </div>
      <div v-else-if="column.dataIndex === 'size'">
        {{
          record.status === 'available'
            ? tableType.includes(record.type)
              ? `${record.row_count}行，${record.col_count}列`
              : `${record.word_count}字符`
            : '--'
        }}
      </div>
      <div v-else v-ellipse-tooltip.right>{{ text || '--' }}</div>
    </template>
  </a-table>
  <a-modal
    v-model:open="visible.delete"
    centered
    :title="`确定删除文件“${currentRecord.name}”？`"
    :loading="deleteLoading"
    @ok="confirmDelete"
  >
    <p>删除后不可恢复</p>
  </a-modal>
</template>
<style scoped lang="less">
  .header {
    height: 40px;
    height: 52px;
    line-height: 40px;
    border-bottom: 1px solid #ccc;
    padding-bottom: 12px;
  }
</style>
