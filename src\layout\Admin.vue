<template>
  <a-layout :style="{ height: '100vh' }" v-show="route.name !== undefined">
    <template v-if="route.name !== 'intelligent-preview'">
      <a-layout-header class="header" style="padding: 0; background: #001529">
        <div class="layout-header">
          <div class="left" :style="{ width: collapsed ? '80px' : '218px' }">
            <div v-show="!collapsed" class="title">
              <div class="logo"></div>
              <div class="color-#fff font-size-8">AI 中台</div>
            </div>
            <div class="trigger" @click="() => (collapsed = !collapsed)">
              <MenuUnfoldOutlined v-if="collapsed" />
              <MenuFoldOutlined v-else />
            </div>
          </div>
          <div class="mr-20px cursor-pointer color-#fff flex grid-items-center">
            {{ name }}
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="logout" @click="handleLogout"> 退出 </a-menu-item>
                </a-menu>
              </template>
              <a-button class="operation-btn" :style="{ marginLeft: '10px' }" shape="circle">
                <UserOutlined />
              </a-button>
            </a-dropdown>
          </div>
        </div>
      </a-layout-header>
      <a-layout>
        <a-layout-sider v-model:collapsed="collapsed" class="overflow-scroll" width="200" style="overflow: auto">
          <a-menu
            v-model:selected-keys="selectedKeys"
            v-model:open-keys="openKeys"
            theme="dark"
            mode="inline"
            :scroll="{ y: '300px' }"
            :style="{ height: '100%', borderRight: 0 }"
            @click="handleMenuClick"
          >
            <template v-for="item in useStore().state.routers" :key="item.name">
              <template v-if="item.children && item.children.filter((i: any) => !i.hidden).length">
                <a-sub-menu v-if="!item.hidden" :key="item.path">
                  <template #title>
                    <svg class="menu-icon" aria-hidden="true">
                      <!-- iconfont图表 新增菜单记得更新 iconfont文件 @/assets/font/iconfont -->
                      <use :xlink:href="`${item.icon}`"></use>
                    </svg>
                    <span v-if="!collapsed">
                      {{ item.meta.breadcrumb }}
                    </span>
                  </template>
                  <template v-for="i in item.children" :key="i.path">
                    <a-menu-item v-if="!i.hidden" :key="i.path">{{ i.meta.breadcrumb }}</a-menu-item>
                  </template>
                </a-sub-menu>
              </template>
              <template v-else>
                <a-menu-item v-if="!item.hidden" :key="item.path">
                  <svg class="menu-icon" aria-hidden="true">
                    <!-- iconfont图表 新增菜单记得更新 iconfont文件 @/assets/font/iconfont -->
                    <use :xlink:href="`${item.icon}`"></use>
                  </svg>
                  <span v-if="!collapsed">{{ item.meta.breadcrumb }}</span>
                </a-menu-item>
              </template>
            </template>
          </a-menu>
        </a-layout-sider>
        <a-layout-content :style="{ background: '#f5f5f5', padding: '24px', margin: 0, minHeight: '280px' }">
          <a-breadcrumb style="margin: 5px 0">
            <a-breadcrumb-item v-for="item in breadcrumb" :key="item">
              <a-button type="text" :disabled="item.disabled" style="padding: 5px" @click="router.push(item.path)">{{
                item.title
              }}</a-button>
            </a-breadcrumb-item>
          </a-breadcrumb>
          <div
            class="container"
            :style="{
              padding: isColumnLayout ? 0 : '12px',
              height: `${breadcrumb.length > 0 ? 'calc(100% - 32px)' : '100%'}`,
              background: isColumnLayout ? '' : '#fff',
            }"
          >
            <router-view />
          </div>
        </a-layout-content>
      </a-layout>
    </template>
    <router-view v-else />
  </a-layout>
</template>

<script lang="ts" setup>
  import { ref, computed, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { MenuUnfoldOutlined, MenuFoldOutlined, UserOutlined } from '@ant-design/icons-vue';
  import { logout } from '@/api/login';
  import { getLocalItem } from '../utils/common';
  import { message } from 'ant-design-vue';
  import { JumpToCasLogin } from '@/utils/jumpToCas';
  import { useStore } from '@/stores/universal';

  const name = ref('');
  const selectedKeys = ref<string[]>(['/']);
  const openKeys = ref<string[]>(['/']);
  const router = useRouter();
  const route = useRoute();
  //
  const breadcrumb = computed(() => {
    const matchedRoutes = route.matched.filter((route) => route.meta && route.meta.breadcrumb);
    if (route.name === 'ClassRoom-addEdit') {
      //@ts-expect-error\
      route.matched.find((route) => route.name === 'ClassRoom-addEdit').meta.breadcrumb =
        `${route.query.classId ? '编辑' : '创建'}课程`;
    }
    return matchedRoutes.map((route) => {
      return {
        title: route.meta.breadcrumb,
        path: route.path,
        disabled: !route.components,
      };
    });
  });
  const isColumnLayout = computed(
    () =>
      ['/label', '/ClassRoom/answer'].includes(route.path) || (route.path.includes('/ClassRoom/') && route.params.id),
  );
  const collapsed = ref(false);
  const handleMenuClick = (item: { key: string }) => {
    router.push(item.key);
  };
  const handleLogout = () => {
    logout().then(() => {
      localStorage.clear();
      message.success('退出成功');
      JumpToCasLogin();
    });
  };

  // watch(
  //   () => route.query,
  //   (query) => {
  //     if (query.classId) {
  //       console.log(route);
  //       route.meta.breadcrumb = '编辑虚拟课堂';
  //       // router.go(0);
  //     }
  //   },
  //   { deep: true, immediate: true },
  // );
  watch(
    () => route,
    () => {
      const user = getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO');
      selectedKeys.value = [route.path];
      if (user) {
        const userInfo = JSON.parse(user);
        name.value = userInfo.nickname || userInfo.username || userInfo.phone;
      }
    },
    { deep: true, immediate: true },
  );
</script>

<style scoped lang="less">
  .logo {
    width: 16px;
    height: 16px;
    background-image: url('../assets/logo.png');
    // margin: 10px;
    // background-size: cover;
  }

  .layout-header {
    .flex-mode(row, space-between);

    height: 100%;

    .left {
      .flex-mode(row);

      .title {
        width: 200px;
        .flex-mode(row);
      }

      .trigger {
        font-size: 18px;
        line-height: 64px;
        color: #fff;
        // padding: 0 12px;
        cursor: pointer;
        transition: color 0.3s;

        &:hover {
          color: #1890ff;
        }
      }

      .logo {
        width: 32px;
        height: 32px;
        margin: 10px;
        background-image: url('../assets/logo.png');
        background-size: cover;
      }
    }

    .right {
      margin-right: 20px;
      cursor: pointer;
    }
  }

  .menu-icon {
    width: 14px;
    height: 14px;
    margin-right: 5px;
    overflow: hidden;
    vertical-align: -0.15em;
    fill: currentcolor;
  }
  :deep(.ant-breadcrumb-separator) {
    margin-inline: 5px;
  }
  :deep(.ant-layout-sider-children) {
    .overflow-scroll;
  }
  .menu-container::-webkit-scrollbar {
    width: 6px;
  }
  .menu-container::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }
  .menu-container::-webkit-scrollbar-track {
    background: #f0f0f0;
  }
</style>
