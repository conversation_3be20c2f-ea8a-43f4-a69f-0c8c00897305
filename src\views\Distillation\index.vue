<script setup lang="ts">
  import { CustomForm } from '@/components';
  import type { IFormItem, IPage } from '@/interface';
  import { TABLE_PAGINATION } from '@/json/common';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
  import { convertIsoTimeToLocalTime, formatTime } from '@/utils/common';
  import { deleteDistillationTask, featchDistillationList, stopDistillationTask } from '@/api/distillation';
  import { DistillationStatus, STATUS_MAP } from './Components/index';
  import { useRoute, useRouter } from 'vue-router';
  import type { IDistillationItems } from '@/interface/distillation';
  import { message } from 'ant-design-vue';
  const route = useRoute();
  const router = useRouter();
  const loading = ref(false);
  const stopLoad = ref(false);
  const DEFAULT_SEARCHSTATE = {
    name: undefined,
    status: undefined,
  };
  const searchState = reactive({ ...DEFAULT_SEARCHSTATE });
  const tableHeight = ref(0);
  const formConfig: IFormItem[] = [
    {
      field: 'name',
      type: 'input',
      label: '任务名称',
      placeholder: '请输入',
    },
    {
      field: 'status',
      type: 'select',
      label: '状态',
      options: DistillationStatus,
      placeholder: '请输入',
    },
  ];
  const columns: ColumnType[] = [
    { title: '任务名称', dataIndex: 'name', fixed: 'left' },
    { title: '教师模型', dataIndex: 'teacher_model_name' },
    { title: '学生模型', dataIndex: 'student_model_name' },
    { title: '计算资源', dataIndex: 'source' },
    { title: '输出模型', dataIndex: 'output_name', width: 120 },
    { title: '状态', dataIndex: 'status' },
    { title: '创建时间', dataIndex: 'created_at', width: 180 },
    { title: '更新时间', dataIndex: 'updated_at', width: 180 },
    { title: '运行时长', dataIndex: 'run_time', width: 120 },
    { title: '操作', dataIndex: 'operation', fixed: 'right', width: 140 },
  ];
  const pagination = reactive({ ...TABLE_PAGINATION });
  const pageParame: IPage = reactive({ page: 1, limit: 10 });
  const dataSource = reactive<IDistillationItems[]>([]);
  const timer = ref();

  const getList = async () => {
    loading.value = true;
    try {
      const data = (await featchDistillationList({
        ...pageParame,
        ...searchState,
      })) as IPage & {
        total: number;
        items: IDistillationItems[];
      };
      const { page, total, items } = data;
      dataSource.length = 0;
      dataSource.push(...items);
      Object.assign(pagination, { current: page, total });
      if (timer.value) {
        clearInterval(timer.value);
        timer.value = null;
      }
      startTaskListRR();
      // Object.assign(statusState, { list: data.finished });
      loading.value = false;
    } catch {
      loading.value = false;
    }
  };
  const startTaskListRR = () => {
    timer.value = setInterval(async () => {
      const data = await featchDistillationList({
        ...{ page: pageParame.page, limit: pageParame.limit },
        ...searchState,
      });
      const { total, items: list } = data;
      dataSource.length = 0;
      dataSource.push(...list);
      Object.assign(pagination, { current: pageParame.page, total: total });
    }, 10000);
  };
  const handleAdd = () => {
    router.push('/distillation/add');
  };

  const taskInfo = (record: { id: string; output_model_id: string }, type?: string) => {
    const { id, output_model_id } = record;
    router.push({
      path: '/distillation/detail',
      query: { ...route.query, taskid: id, type, output_model_id, model_category: 'distillation' },
    });
  };

  const deleteTaskItem = async (id: string) => {
    await deleteDistillationTask(id);
    message.success('已删除');
    getList();
  };
  const handleStopTask = async (id: string) => {
    await stopDistillationTask(id);
    message.success('已停止');
    getList();
  };

  const getTableHeight = () => {
    const tableItem = document.querySelector('.container');
    tableHeight.value = tableItem?.clientHeight as number;
  };
  const onFinish = (values: Record<string, string>) => {
    const searchConfig: { [key: string]: string } = {};
    for (let key in values) {
      searchConfig[key] = values[key];
    }
    Object.assign(searchState, { ...searchConfig });
    pageParame.page = 1;
    getList();
  };
  const onRest = () => {
    Object.assign(pagination, { ...TABLE_PAGINATION });
    Object.assign(pageParame, { page: 1, limit: 10 });
    Object.assign(searchState, { ...DEFAULT_SEARCHSTATE });
    getList();
  };

  const toggleTable = (_pagination: TablePaginationConfig) => {
    let { current, pageSize } = _pagination;
    console.log(current, pageSize);
    Object.assign(pagination, { current, pageSize });
    Object.assign(pageParame, { page: current, limit: pageSize });
    getList();
  };
  onMounted(() => {
    getTableHeight();
  });

  onUnmounted(() => {
    clearInterval(timer.value);
    timer.value = null;
  });

  watch(
    () => route.path,
    (path) => {
      if (path === '/distillation') {
        getList();
      } else {
        clearInterval(timer.value);
        timer.value = null;
      }
    },
    { deep: true, immediate: true },
  );
</script>

<template>
  <template v-if="['Distillation_add', 'Distillation_detail'].includes(String(route.name))">
    <router-view></router-view>
  </template>
  <template v-else>
    <CustomForm style="margin-bottom: 0" :form-items="formConfig" @on-finish="onFinish" @on-rest="onRest" />
    <div class="table-button">
      <a-button type="primary" @click="handleAdd"><PlusOutlined />创建蒸馏任务</a-button>
    </div>
    <a-table
      :data-source="dataSource"
      :columns="columns"
      :pagination="pagination"
      :loading="loading"
      :scroll="{ y: tableHeight - 250, x: 'max-content' }"
      @change="toggleTable"
    >
      <template #bodyCell="{ column, record, text }">
        <div v-if="column.dataIndex === 'operation'" class="operation-box">
          <a @click="taskInfo(record)">查看</a>
          <a-button
            v-if="record.status === STATUS_MAP.RUNNING"
            :loading="stopLoad"
            type="link"
            style="padding: 0; margin-right: 10px"
            @click="handleStopTask(record.id)"
            >停止</a-button
          >
          <a v-if="record.status === STATUS_MAP.COMPLETED" @click="taskInfo(record, 'deploy')">部署</a>
          <a-popconfirm
            v-if="![STATUS_MAP.RUNNING].includes(record.status)"
            title="确定删除这条任务吗?"
            @confirm="deleteTaskItem(record.id)"
          >
            <a class="del-btn">删除</a>
          </a-popconfirm>
        </div>
        <div v-else-if="column.dataIndex === 'source'">公共资源</div>
        <div v-else-if="['created_at', 'updated_at'].includes(column.dataIndex)">
          {{ convertIsoTimeToLocalTime(text) }}
        </div>
        <div v-else-if="column.dataIndex === 'run_time'">
          {{ formatTime(text) }}
        </div>
        <div v-else-if="column.dataIndex === 'status'">
          <a-tag :color="DistillationStatus.find((item) => item.value === text)?.color">{{
            DistillationStatus.find((item) => item.value === text)?.label
          }}</a-tag>
        </div>
        <div v-else v-ellipse-tooltip.right>{{ text }}</div>
      </template>
    </a-table>
  </template>
</template>

<style scoped lang="less">
  .table-button {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
  }
  :deep(.ant-table-cell-ellipsis div) {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
