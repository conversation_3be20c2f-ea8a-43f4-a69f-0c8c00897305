<script setup lang="ts">
  import { ref } from 'vue';
  import { Tooltip, message } from 'ant-design-vue';
  import { QuestionCircleFilled } from '@ant-design/icons-vue';
  import Interactive from './Interactive.vue';
  import { createTask, createUnslothTask, createSklearnTask, createP2lTask } from '@/api/model';
  import { type ICreateTaskProps } from '@/interface/model';
  import { useRouter } from 'vue-router';
  import type { IModelFormState, ITrainFormState } from '@/interface/model';
  import { machineLearningList } from '../Manage/index';
  import { sklearn_paramters } from '.';
  const type = ref(0);
  const interactiveRef = ref();
  interface IProps {
    model: Partial<IModelFormState>;
    closeVisible: () => void;
  }
  const props = defineProps<IProps>();
  const router = useRouter();
  const loading = ref(false);
  const onConfirm = async () => {
    await interactiveRef.value.validateFields();
    loading.value = true;
    const data: ITrainFormState = JSON.parse(JSON.stringify(interactiveRef.value.formState));
    const { train_parameter } = data;
    const { general_parameters, unsloth_parameters } = train_parameter;
    const train_parameter_flat = {};
    if (machineLearningList.includes(props.model.category!)) {
      // kmeans 没有数据标签
      if (props.model.category === 'sklearn-KMeans') {
        data.dataset_parameter.numeric_labels = null;
      } else {
        const labels = JSON.parse(JSON.stringify(data.dataset_parameter.numeric_labels));
        data.dataset_parameter.numeric_labels = [labels];
      }
      //@ts-expect-error
      Object.assign(train_parameter_flat, { ...sklearn_paramters[props.model.category!].value });
    } else {
      Object.assign(
        train_parameter_flat,
        data.tf_parameter.type === 'unsloth'
          ? JSON.parse(JSON.stringify(unsloth_parameters))
          : JSON.parse(JSON.stringify(general_parameters)),
      );
      //@ts-expect-error
      if (isNaN(general_parameters.learning_rate) || Number(general_parameters!.learning_rate) < 0) {
        message.warn('学习率格式错误，请输入正数');
        return;
      }

      Object.keys(interactiveRef.value.disableState).forEach((key) => {
        if (interactiveRef.value.disableState[key]) {
          //@ts-expect-error
          Object.assign(train_parameter_flat, { ...train_parameter[key] });
        }
      });
    }

    if (data.tf_parameter && data.tf_parameter.type !== 'unsloth') {
      // @ts-expect-error
      const computed_type = train_parameter_flat.computed_type;
      if (['bf16', 'fp16'].includes(computed_type!)) {
        // @ts-expect-error
        train_parameter_flat['computed_type'] = true;
        // @ts-expect-error
        delete train_parameter_flat.computed_type;
      }
    }
    const datasetParams = data.dataset_parameter;
    // @ts-expect-error
    const params: ICreateTaskProps = {
      ...data,
      train_parameter: train_parameter_flat,
    };
    if (data.gpu_number_list !== undefined) {
      params.gpu_number_list = [data.gpu_number_list];
    }
    try {
      // machineLearningList.includes(props.model.category!)
      //   ? await createSklearnTask(params)
      //   : props.model.category === 'p2l'
      //     ? await createP2lTask(params)
      //     : await createTask(params);

      if (machineLearningList.includes(props.model.category!)) {
        await createSklearnTask(params);
      } else if (props.model.category === 'p2l') {
        await createP2lTask(params);
      } else if (data.tf_parameter && data.tf_parameter.type === 'unsloth') {
        await createUnslothTask(params);
      } else {
        await createTask(params);
      }
      message.success('创建成功');
      props.closeVisible();
      loading.value = true;
      router.push({ path: machineLearningList.includes(props.model.category!) ? '/train' : '/fine-tuned' });
    } catch {
      // message.warn(`创建失败 ${e}`)
    }
  };
</script>

<template>
  <div class="flex flex-col h-100%">
    <div class="flex-1 overflow-y-sorcll container">
      <a-radio-group v-model:value="type" style="margin-bottom: 16px">
        <a-radio-button :value="0">
          交互式配置
          <Tooltip>
            <template #title>
              提供直观的图形用户界面（GUI）等可视化界面，用户依据界面提示输入指令、设置参数或选择选项，轻松创建模型训练任务。
            </template>
            <QuestionCircleFilled />
          </Tooltip>
        </a-radio-button>
        <!-- <a-radio-button :value="1">
          脚本式配置
          <Tooltip>
            <template #title>
              用户编写包含系列指令与操作步骤的脚本，脚本执行时按预定顺序自动完成配置，高效便捷。
            </template>
            <QuestionCircleFilled />
          </Tooltip>
        </a-radio-button> -->
      </a-radio-group>
      <Interactive v-if="type === 0" ref="interactiveRef" :model="props.model" />
    </div>
    <div class="h-36px mt-10px flex flex-justify-end">
      <a-button type="primary" :loading="loading" @click="onConfirm">确定</a-button>
    </div>
  </div>
</template>

<style scoped lang="less">
  .container {
    height: calc(100% - 36px);
    overflow-y: scroll;
    .overflow-scroll;
  }
</style>
