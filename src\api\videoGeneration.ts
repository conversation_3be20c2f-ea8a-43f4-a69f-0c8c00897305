import request from '@/utils/request';
import { getEnv } from '@/utils';

const { VITE_APP_AVATAR_URL } = getEnv();

const AVATAR = VITE_APP_AVATAR_URL;

// 定义宽松的对象类型
type TLooseObject = Record<string, unknown>;

interface ChatProps {
  user_id: number;
  history_id: string;
  content: string;
  image_url_list: [] | null;
}

// 图文生生视频
export function sendMess(data: TLooseObject) {
  return request.$Axios.post(`/avatar-back-end/video/image_text_video[${AVATAR}]`, data);
}

// 创建历史记录ID
export function createHistory(data: TLooseObject) {
  return request.$Axios.post(`/avatar-back-end/v2/video/history/create[${AVATAR}]`, data);
}

// 对话
export function chatInHistory(data: ChatProps) {
  return request.$Axios.post(`/avatar-back-end/v2/video/history/chat[${AVATAR}]`, data);
}

// 查询历史返回结果
export function searchHistory(data: TLooseObject) {
  return request.$Axios.get(`/avatar-back-end/v2/video/generation/query[${AVATAR}]`, data);
}

// 获取历史记录列表
export function fetchHistoryList(data: TLooseObject) {
  return request.$Axios.get(`/avatar-back-end/v2/video/history/list[${AVATAR}]`, data);
}

// 删除某个历史记录
export function deleteHistory(data: TLooseObject) {
  return request.$Axios.del(`/avatar-back-end/v2/video/history/${data.history_id}[${AVATAR}]`);
}
